import React, { createContext, useContext, useState, useEffect } from 'react'

type Language = 'ar' | 'en'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
  isRTL: boolean
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Simple translation function - can be expanded with proper i18n later
const translations = {
  ar: {
    // Add Arabic translations as needed
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
    cancel: 'إلغاء',
    save: 'حفظ',
    edit: 'تعديل',
    delete: 'حذف',
    view: 'عرض',
    search: 'بحث',
    filter: 'تصفية',
    export: 'تصدير',
    import: 'استيراد',
    refresh: 'تحديث',
    settings: 'الإعدادات',
    profile: 'الملف الشخصي',
    logout: 'تسجيل الخروج',
    dashboard: 'لوحة التحكم',
    employees: 'الموظفون',
    departments: 'الأقسام',
    reports: 'التقارير',
    analytics: 'التحليلات',
    notifications: 'الإشعارات',
    help: 'المساعدة',
    about: 'حول',
    contact: 'اتصل بنا',
    home: 'الرئيسية',
    back: 'رجوع',
    next: 'التالي',
    previous: 'السابق',
    submit: 'إرسال',
    reset: 'إعادة تعيين',
    clear: 'مسح',
    close: 'إغلاق',
    open: 'فتح',
    yes: 'نعم',
    no: 'لا',
    ok: 'موافق',
    confirm: 'تأكيد',
    warning: 'تحذير',
    info: 'معلومات',
    name: 'الاسم',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    address: 'العنوان',
    date: 'التاريخ',
    time: 'الوقت',
    status: 'الحالة',
    active: 'نشط',
    inactive: 'غير نشط',
    enabled: 'مفعل',
    disabled: 'معطل',
    public: 'عام',
    private: 'خاص',
    draft: 'مسودة',
    published: 'منشور',
    pending: 'في الانتظار',
    approved: 'موافق عليه',
    rejected: 'مرفوض',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    total: 'المجموع',
    count: 'العدد',
    amount: 'المبلغ',
    price: 'السعر',
    quantity: 'الكمية',
    description: 'الوصف',
    details: 'التفاصيل',
    category: 'الفئة',
    type: 'النوع',
    priority: 'الأولوية',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    urgent: 'عاجل',
    normal: 'عادي',
    title: 'العنوان',
    content: 'المحتوى',
    message: 'الرسالة',
    comment: 'التعليق',
    note: 'ملاحظة',
    tag: 'العلامة',
    tags: 'العلامات',
    file: 'الملف',
    files: 'الملفات',
    image: 'الصورة',
    images: 'الصور',
    document: 'الوثيقة',
    documents: 'الوثائق',
    attachment: 'المرفق',
    attachments: 'المرفقات',
    link: 'الرابط',
    links: 'الروابط',
    url: 'الرابط',
    website: 'الموقع الإلكتروني',
    username: 'اسم المستخدم',
    password: 'كلمة المرور',
    login: 'تسجيل الدخول',
    register: 'التسجيل',
    signup: 'إنشاء حساب',
    signin: 'تسجيل الدخول',
    signout: 'تسجيل الخروج',
    account: 'الحساب',
    user: 'المستخدم',
    users: 'المستخدمون',
    role: 'الدور',
    roles: 'الأدوار',
    permission: 'الصلاحية',
    permissions: 'الصلاحيات',
    access: 'الوصول',
    security: 'الأمان',
    privacy: 'الخصوصية',
    terms: 'الشروط',
    policy: 'السياسة',
    agreement: 'الاتفاقية',
    license: 'الترخيص',
    version: 'الإصدار',
    update: 'تحديث',
    upgrade: 'ترقية',
    download: 'تحميل',
    upload: 'رفع',
    install: 'تثبيت',
    uninstall: 'إلغاء التثبيت',
    configure: 'تكوين',
    setup: 'إعداد',
    maintenance: 'الصيانة',
    backup: 'النسخ الاحتياطي',
    restore: 'الاستعادة',
    sync: 'المزامنة',
    connect: 'الاتصال',
    disconnect: 'قطع الاتصال',
    online: 'متصل',
    offline: 'غير متصل',
    available: 'متاح',
    unavailable: 'غير متاح',
    busy: 'مشغول',
    free: 'متاح',
    occupied: 'مشغول',
    vacant: 'شاغر',
    full: 'ممتلئ',
    empty: 'فارغ',
    new: 'جديد',
    old: 'قديم',
    recent: 'حديث',
    latest: 'الأحدث',
    first: 'الأول',
    last: 'الأخير',
    all: 'الكل',
    none: 'لا شيء',
    some: 'بعض',
    many: 'كثير',
    few: 'قليل',
    more: 'المزيد',
    less: 'أقل',
    most: 'الأكثر',
    least: 'الأقل',
    best: 'الأفضل',
    worst: 'الأسوأ',
    better: 'أفضل',
    worse: 'أسوأ',
    good: 'جيد',
    bad: 'سيء',
    excellent: 'ممتاز',
    poor: 'ضعيف',
    average: 'متوسط',
    fair: 'عادل',
    great: 'رائع',
    awesome: 'رائع',
    amazing: 'مذهل',
    wonderful: 'رائع',
    fantastic: 'رائع',
    perfect: 'مثالي',
    terrible: 'فظيع',
    horrible: 'مروع',
    awful: 'فظيع',
    nice: 'لطيف',
    cool: 'رائع',
    hot: 'ساخن',
    cold: 'بارد',
    warm: 'دافئ',
    fresh: 'طازج',
    stale: 'قديم',
    clean: 'نظيف',
    dirty: 'متسخ',
    clear: 'واضح',
    unclear: 'غير واضح',
    visible: 'مرئي',
    invisible: 'غير مرئي',
    hidden: 'مخفي',
    shown: 'معروض',
    open: 'مفتوح',
    closed: 'مغلق',
    locked: 'مقفل',
    unlocked: 'غير مقفل',
    secure: 'آمن',
    insecure: 'غير آمن',
    safe: 'آمن',
    unsafe: 'غير آمن',
    dangerous: 'خطير',
    risky: 'محفوف بالمخاطر',
    stable: 'مستقر',
    unstable: 'غير مستقر',
    reliable: 'موثوق',
    unreliable: 'غير موثوق',
    valid: 'صالح',
    invalid: 'غير صالح',
    correct: 'صحيح',
    incorrect: 'غير صحيح',
    right: 'صحيح',
    wrong: 'خطأ',
    true: 'صحيح',
    false: 'خطأ',
    real: 'حقيقي',
    fake: 'مزيف',
    original: 'أصلي',
    copy: 'نسخة',
    duplicate: 'مكرر',
    unique: 'فريد',
    common: 'شائع',
    rare: 'نادر',
    special: 'خاص',
    general: 'عام',
    specific: 'محدد',
    detailed: 'مفصل',
    brief: 'موجز',
    short: 'قصير',
    long: 'طويل',
    quick: 'سريع',
    slow: 'بطيء',
    fast: 'سريع',
    instant: 'فوري',
    immediate: 'فوري',
    delayed: 'متأخر',
    early: 'مبكر',
    late: 'متأخر',
    ontime: 'في الوقت المحدد',
    overdue: 'متأخر',
    expired: 'منتهي الصلاحية',
    valid: 'صالح',
    current: 'حالي',
    past: 'ماضي',
    future: 'مستقبل',
    present: 'حاضر',
    today: 'اليوم',
    yesterday: 'أمس',
    tomorrow: 'غداً',
    week: 'أسبوع',
    month: 'شهر',
    year: 'سنة',
    day: 'يوم',
    hour: 'ساعة',
    minute: 'دقيقة',
    second: 'ثانية',
    morning: 'صباح',
    afternoon: 'بعد الظهر',
    evening: 'مساء',
    night: 'ليل',
    midnight: 'منتصف الليل',
    noon: 'ظهر',
    dawn: 'فجر',
    dusk: 'غسق',
    sunrise: 'شروق الشمس',
    sunset: 'غروب الشمس',
    monday: 'الاثنين',
    tuesday: 'الثلاثاء',
    wednesday: 'الأربعاء',
    thursday: 'الخميس',
    friday: 'الجمعة',
    saturday: 'السبت',
    sunday: 'الأحد',
    january: 'يناير',
    february: 'فبراير',
    march: 'مارس',
    april: 'أبريل',
    may: 'مايو',
    june: 'يونيو',
    july: 'يوليو',
    august: 'أغسطس',
    september: 'سبتمبر',
    october: 'أكتوبر',
    november: 'نوفمبر',
    december: 'ديسمبر'
  },
  en: {
    // English translations (fallback)
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    import: 'Import',
    refresh: 'Refresh',
    settings: 'Settings',
    profile: 'Profile',
    logout: 'Logout',
    dashboard: 'Dashboard',
    employees: 'Employees',
    departments: 'Departments',
    reports: 'Reports',
    analytics: 'Analytics',
    notifications: 'Notifications',
    help: 'Help',
    about: 'About',
    contact: 'Contact',
    home: 'Home',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    reset: 'Reset',
    clear: 'Clear',
    close: 'Close',
    open: 'Open',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    confirm: 'Confirm',
    warning: 'Warning',
    info: 'Info'
    // Add more English translations as needed
  }
}

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('ar')

  useEffect(() => {
    // Load language from localStorage or browser preference
    const savedLanguage = localStorage.getItem('language') as Language
    if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
      setLanguage(savedLanguage)
    } else {
      // Default to Arabic
      setLanguage('ar')
    }
  }, [])

  useEffect(() => {
    // Save language to localStorage
    localStorage.setItem('language', language)
    
    // Update document direction
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = language
  }, [language])

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key
  }

  const isRTL = language === 'ar'

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

export default LanguageContext
