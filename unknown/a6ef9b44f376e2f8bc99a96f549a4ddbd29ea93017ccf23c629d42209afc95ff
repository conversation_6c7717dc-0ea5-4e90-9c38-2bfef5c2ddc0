# Generated manually for adding SUPERADMIN role

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0005_add_kpi_algorithm_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='role',
            name='name',
            field=models.Char<PERSON><PERSON>(choices=[('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Super Administrator'), ('<PERSON><PERSON><PERSON>', 'Administrator'), ('HR_MANAGER', 'HR Manager'), ('DEPARTMENT_MANAGER', 'Department Manager'), ('PROJECT_MANAGER', 'Project Manager'), ('FINANCE_MANAGER', 'Finance Manager'), ('EMPLOYEE', 'Employee'), ('INTERN', 'Intern')], max_length=50, unique=True),
        ),
    ]
