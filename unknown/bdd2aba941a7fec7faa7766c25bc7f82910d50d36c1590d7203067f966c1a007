# Generated by Django 4.2.7 on 2025-06-05 05:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0006_add_superadmin_role'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('first_name', models.Char<PERSON><PERSON>(max_length=100)),
                ('last_name', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('customer_type', models.Char<PERSON>ield(choices=[('individual', 'Individual'), ('business', 'Business'), ('enterprise', 'Enterprise')], default='individual', max_length=20)),
                ('status', models.Char<PERSON>ield(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('suspended', 'Suspended'), ('prospect', 'Prospect')], default='prospect', max_length=20)),
                ('company_name', models.CharField(blank=True, max_length=200)),
                ('company_size', models.CharField(blank=True, max_length=50)),
                ('industry', models.CharField(blank=True, max_length=100)),
                ('address_line1', models.CharField(blank=True, max_length=255)),
                ('address_line2', models.CharField(blank=True, max_length=255)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state', models.CharField(blank=True, max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('source', models.CharField(blank=True, help_text='How customer found us', max_length=50)),
                ('internal_notes', models.TextField(blank=True)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_contact_date', models.DateTimeField(blank=True, null=True)),
                ('total_orders', models.IntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('satisfaction_score', models.FloatField(default=0.0, help_text='Average satisfaction score')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
