# Generated by Django 4.2.7 on 2025-06-02 08:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0004_kpi_kpicategory_kpitarget_kpialert_kpi_category_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='kpi',
            name='algorithm_config',
            field=models.JSONField(blank=True, default=dict, help_text='Algorithm configuration'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='anomaly_detection',
            field=models.BooleanField(default=True, help_text='Enable anomaly detection'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='auto_recommendations',
            field=models.BooleanField(default=True, help_text='Generate AI recommendations'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='current_value',
            field=models.DecimalField(blank=True, decimal_places=4, help_text='Current KPI value', max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='kpi',
            name='last_updated',
            field=models.DateTimeField(blank=True, help_text='When the current value was last updated', null=True),
        ),
        migrations.AddField(
            model_name='kpi',
            name='ml_model',
            field=models.ForeignKey(blank=True, help_text='ML model for predictions', null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.mlmodel'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='updated_by',
            field=models.ForeignKey(blank=True, help_text='Who last updated the value', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_kpis', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='use_ml_prediction',
            field=models.BooleanField(default=False, help_text='Use ML for KPI prediction'),
        ),
        migrations.AlterField(
            model_name='mlprediction',
            name='prediction_type',
            field=models.CharField(choices=[('employee_performance', 'Employee Performance'), ('turnover_risk', 'Turnover Risk'), ('project_success', 'Project Success'), ('budget_forecast', 'Budget Forecast'), ('resource_demand', 'Resource Demand'), ('kpi_forecast', 'KPI Forecast'), ('kpi_anomaly', 'KPI Anomaly Detection'), ('kpi_optimization', 'KPI Optimization')], max_length=30),
        ),
    ]
