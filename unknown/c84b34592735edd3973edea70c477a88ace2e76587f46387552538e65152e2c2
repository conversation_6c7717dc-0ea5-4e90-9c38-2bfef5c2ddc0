# Generated by Django 4.2.7 on 2025-06-01 11:35

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0003_enterprise_features'),
    ]

    operations = [
        migrations.CreateModel(
            name='KPI',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('name_ar', models.Char<PERSON>ield(help_text='Arabic name', max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(help_text='Arabic description')),
                ('measurement_type', models.CharField(choices=[('NUMBER', 'Number'), ('PERCENTAGE', 'Percentage'), ('CURRENCY', 'Currency'), ('RATIO', 'Ratio'), ('SCORE', 'Score'), ('TIME', 'Time Duration')], max_length=20)),
                ('unit', models.CharField(blank=True, help_text='e.g., %, $, hours', max_length=50)),
                ('unit_ar', models.CharField(blank=True, help_text='Arabic unit', max_length=50)),
                ('frequency', models.CharField(choices=[('DAILY', 'Daily'), ('WEEKLY', 'Weekly'), ('MONTHLY', 'Monthly'), ('QUARTERLY', 'Quarterly'), ('YEARLY', 'Yearly')], max_length=20)),
                ('trend_direction', models.CharField(choices=[('UP', 'Higher is Better'), ('DOWN', 'Lower is Better'), ('TARGET', 'Target Value')], max_length=10)),
                ('formula', models.TextField(blank=True, help_text='Calculation formula or description')),
                ('data_source', models.CharField(blank=True, help_text='Source of data', max_length=200)),
                ('calculation_method', models.TextField(blank=True)),
                ('target_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('warning_threshold', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('critical_threshold', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('DRAFT', 'Draft'), ('ARCHIVED', 'Archived')], default='ACTIVE', max_length=20)),
                ('is_automated', models.BooleanField(default=False, help_text='Automatically calculated')),
                ('automation_config', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'KPI',
                'verbose_name_plural': 'KPIs',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='KPICategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('FINANCIAL', 'Financial'), ('HR', 'Human Resources'), ('OPERATIONS', 'Operations'), ('SALES', 'Sales & Marketing'), ('CUSTOMER', 'Customer Service'), ('QUALITY', 'Quality Management'), ('INNOVATION', 'Innovation & Development'), ('COMPLIANCE', 'Compliance & Risk'), ('SUSTAINABILITY', 'Sustainability'), ('STRATEGIC', 'Strategic Goals')], max_length=50, unique=True)),
                ('name_ar', models.CharField(help_text='Arabic name', max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True, help_text='Arabic description')),
                ('icon', models.CharField(default='BarChart3', max_length=50)),
                ('color', models.CharField(default='from-blue-500 to-blue-600', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'KPI Categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='KPITarget',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('target_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('target_type', models.CharField(choices=[('ABSOLUTE', 'Absolute Value'), ('PERCENTAGE_CHANGE', 'Percentage Change'), ('RELATIVE', 'Relative to Baseline')], default='ABSOLUTE', max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('description', models.TextField(blank=True)),
                ('is_stretch_goal', models.BooleanField(default=False)),
                ('weight', models.DecimalField(decimal_places=2, default=1.0, max_digits=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_kpi_targets', to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('kpi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='targets', to='ems.kpi')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='KPIAlert',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('alert_type', models.CharField(choices=[('THRESHOLD_BREACH', 'Threshold Breach'), ('TARGET_MISSED', 'Target Missed'), ('TREND_CHANGE', 'Trend Change'), ('DATA_QUALITY', 'Data Quality Issue'), ('MISSING_DATA', 'Missing Data')], max_length=20)),
                ('severity', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('message_ar', models.TextField()),
                ('current_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('threshold_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('target_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('ACKNOWLEDGED', 'Acknowledged'), ('RESOLVED', 'Resolved'), ('DISMISSED', 'Dismissed')], default='ACTIVE', max_length=20)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True)),
                ('notification_sent', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_alerts', to='ems.employee')),
                ('kpi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='ems.kpi')),
                ('notified_users', models.ManyToManyField(blank=True, related_name='kpi_alert_notifications', to='ems.employee')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_alerts', to='ems.employee')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='kpi',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='kpis', to='ems.kpicategory'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_kpis', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='owner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owned_kpis', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='kpi',
            name='visible_to_roles',
            field=models.ManyToManyField(blank=True, help_text='Roles that can view this KPI', to='ems.role'),
        ),
        migrations.CreateModel(
            name='KPIValue',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('period_start', models.DateTimeField()),
                ('period_end', models.DateTimeField()),
                ('data_quality_score', models.DecimalField(decimal_places=2, default=100.0, max_digits=5)),
                ('confidence_level', models.DecimalField(decimal_places=2, default=100.0, max_digits=5)),
                ('notes', models.TextField(blank=True)),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('is_estimated', models.BooleanField(default=False)),
                ('source_data', models.JSONField(blank=True, default=dict)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('kpi', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='ems.kpi')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
                ('recorded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recorded_kpi_values', to='ems.employee')),
            ],
            options={
                'ordering': ['-period_start'],
                'unique_together': {('kpi', 'period_start', 'department', 'project', 'employee')},
            },
        ),
    ]
