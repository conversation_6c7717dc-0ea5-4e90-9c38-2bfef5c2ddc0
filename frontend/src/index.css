@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force cache busting - Updated 2025-06-02 */

/* CSS Cache Override - Force Modern Styles */
* {
  box-sizing: border-box !important;
}

html, body {
  margin: 0 !important;
  padding: 0 !important;
  font-family: 'Cairo', 'Tajawal', system-ui, -apple-system, sans-serif !important;
}

/* Modern Glass Morphism EMS Styles */

/* Body with Gradient Background */
body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #1434c2 0%, #764ba2 100%);
  background-attachment: fixed;
  color: #ffffff;
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Glass Morphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Modern Button Styles */
.modern-button {
  background: rgba(17, 16, 16, 0.12);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 12px;
  box-shadow: 0 8px 24px 0 rgba(31, 38, 135, 0.3);
  transition: all 0.3s ease;
  color: #ffffff;
}

.modern-button:hover {
  background: rgba(255, 255, 255, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.35);
  box-shadow: 0 12px 32px 0 rgba(31, 38, 135, 0.4);
  transform: translateY(-2px);
}

/* Modern Input Styles */
.modern-input {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 12px;
  box-shadow: 0 8px 24px 0 rgba(31, 38, 135, 0.3);
  color: #ffffff;
  transition: all 0.3s ease;
}

.modern-input:focus {
  background: rgba(255, 255, 255, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.45);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.4);
  outline: none;
}

.modern-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Modern Card Styles */
.modern-card {
  background: rgba(9, 9, 9, 0.12);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 12px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.4);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

/* Responsive Card Styles */
@media (min-width: 640px) {
  .modern-card {
    border-radius: 16px;
    box-shadow: 0 16px 48px 0 rgba(31, 38, 135, 0.4);
  }

  .modern-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px 0 rgba(31, 38, 135, 0.5);
  }
}

/* Glass Card Styles - Used in new pages - FORCE OVERRIDE */
.glass-card {
  background: rgba(11, 11, 11, 0.1) !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.37) !important;
  transition: all 0.3s ease !important;
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
}

.glass-card:hover {
  background: rgba(84, 78, 78, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 24px 0 rgba(31, 38, 135, 0.45) !important;
}

/* Glass Navigation - Enhanced */
.glass-nav {
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.2) !important;
}

/* Header specific enhancements */
.header-logo {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.header-breadcrumb {
  color: rgba(255, 255, 255, 0.8) !important;
}

.header-breadcrumb.active {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Glass Button Styles - Consolidated */
.glass-button {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  padding: 0.5rem 1rem !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.2) !important;
}

/* Force text visibility in glass buttons */
.glass-button, .glass-button *, .glass-button span, .glass-button div {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 24px 0 rgba(31, 38, 135, 0.3) !important;
  color: #ffffff !important;
}

.glass-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.2) !important;
}

.glass-button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Glass Input Styles - Consolidated */
.glass-input {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  padding: 0.75rem !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.2) !important;
}

.glass-input:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3) !important;
  outline: none !important;
  color: #ffffff !important;
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

.glass-input:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* How It Works Page Specific Styles */
.how-it-works-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  min-height: 100vh !important;
}

/* Force text colors for How It Works components */
.how-it-works-container * {
  color: inherit !important;
}

.how-it-works-container h1,
.how-it-works-container h2,
.how-it-works-container h3,
.how-it-works-container h4,
.how-it-works-container h5,
.how-it-works-container h6 {
  color: #ffffff !important;
}

.how-it-works-container p,
.how-it-works-container span,
.how-it-works-container div {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Badge styling fixes */
.glass-card .badge,
.glass-card [class*="badge"] {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Card title and content fixes */
.glass-card [class*="CardTitle"],
.glass-card [class*="card-title"] {
  color: #ffffff !important;
}

.glass-card [class*="CardContent"],
.glass-card [class*="card-content"] {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Button variant fixes */
.glass-button[data-variant="ghost"] {
  background: transparent !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.glass-button[data-variant="ghost"]:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Gradient text fixes - DISABLED to prevent invisible text */
/* .bg-gradient-to-r {
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
} */

/* Animation fixes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Blob animation fixes */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

/* Tab Navigation Styles */
.glass-tab {
  @apply text-white/70 hover:text-white hover:bg-white/10 data-[state=active]:bg-white/20 data-[state=active]:text-white;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Z-Index Hierarchy for Proper Layering */
/* Base layers: 1-10 */
/* Navigation: 20-30 */
/* Dropdowns/Tooltips: 40-50 */
/* Modals/Overlays: 60-70 */
/* Critical overlays: 80-90 */
/* Emergency overlays: 100+ */

/* Base fixed elements */
.fixed {
  z-index: 10 !important;
}

/* Scrollbar hiding for mobile navigation */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Sidebar specific styles */
.sidebar-nav {
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  background: rgba(0, 0, 0, 0.3) !important;
  border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Sidebar scrollbar styling */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Mobile navigation improvements */
@media (max-width: 1023px) {
  .mobile-nav-bottom {
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    background: rgba(0, 0, 0, 0.4) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }
}

/* RTL support for sidebar */
.rtl .sidebar-nav {
  border-right: none !important;
  border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Content area padding adjustments */
.content-area {
  padding-top: 2rem !important;
  min-height: calc(100vh - 200px) !important;
}

/* Tab Navigation Styles */
.tab-navigation {
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 1rem !important;
  padding: 0.5rem !important;
  margin-bottom: 2rem !important;
}

.tab-button {
  padding: 0.75rem 1.5rem !important;
  border-radius: 0.75rem !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.tab-button::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent) !important;
  transition: left 0.5s ease !important;
}

.tab-button:hover::before {
  left: 100% !important;
}

.tab-button-active {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  color: #ffffff !important;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3) !important;
  transform: translateY(-1px) !important;
}

.tab-button-inactive {
  color: rgba(255, 255, 255, 0.7) !important;
}

.tab-button-inactive:hover {
  color: #ffffff !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-1px) !important;
}

/* Mobile tab navigation */
.mobile-tab-navigation {
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 1rem !important;
  padding: 1rem !important;
  margin-bottom: 2rem !important;
}

/* Tab content animation */
.tab-content {
  animation: fadeInUp 0.6s ease-out forwards !important;
}

/* fadeInUp keyframes already defined above */

/* Tab indicator animation */
.tab-indicator {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6) !important;
  transition: all 0.3s ease !important;
  border-radius: 1px !important;
}

/* Responsive Glass Card Styles */
@media (min-width: 640px) {
  .glass-card {
    border-radius: 12px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45);
  }
}

/* Gradient Backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-alt {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-blue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-purple {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Animated Gradients */
.animated-gradient {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Floating Animation */
.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0% { transform: translate(0, 0px); }
  50% { transform: translate(0, -10px); }
  100% { transform: translate(0, 0px); }
}

/* Fade In Up Animation */
.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation Delays */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-800 {
  animation-delay: 0.8s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-1200 {
  animation-delay: 1.2s;
}

.animation-delay-1400 {
  animation-delay: 1.4s;
}

.animation-delay-1600 {
  animation-delay: 1.6s;
}

/* Text Gradient Animation */
.animate-text-gradient {
  background-size: 300% 300%;
  animation: gradientText 3s ease infinite;
}

@keyframes gradientText {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Gradient X Animation */
.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradientX 4s ease infinite;
}

@keyframes gradientX {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Scale on Hover */
.hover-scale:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

/* Bounce Animation */
.animate-bounce-slow {
  animation: bounceSlow 3s infinite;
}

@keyframes bounceSlow {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -7px, 0);
  }
  90% {
    transform: translate3d(0,-2px,0);
  }
}

/* Slide In Animation */
.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
  opacity: 0;
  transform: translateX(-50px);
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
  opacity: 0;
  transform: translateX(50px);
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Rotate Animation */
.animate-rotate-slow {
  animation: rotateSlow 8s linear infinite;
}

@keyframes rotateSlow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Blob Animation */
.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* blob keyframes already defined above */

/* Pulse Animation */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Glow Effects */
.glow {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
}

.glow-hover:hover {
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
  transition: box-shadow 0.3s ease;
}

/* Shimmer Effect */
.shimmer {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Tilt Effect */
.tilt:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
  transition: transform 0.3s ease;
}

/* Neon Glow Text */
.neon {
  text-shadow: 0 0 5px rgba(102, 126, 234, 0.8),
               0 0 10px rgba(102, 126, 234, 0.8),
               0 0 15px rgba(102, 126, 234, 0.8),
               0 0 20px rgba(102, 126, 234, 0.8);
}

/* Text Styling Fixes */
.text-white {
  color: #ffffff !important;
}

.text-white\/90 {
  color: rgba(255, 255, 255, 0.9) !important;
}

.text-white\/80 {
  color: rgba(255, 255, 255, 0.8) !important;
}

.text-white\/70 {
  color: rgba(255, 255, 255, 0.7) !important;
}

.text-white\/60 {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Better Text Readability */
h1, h2, h3, h4, h5, h6 {
  color: #ffffff;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* Input Text Fixes */
input, textarea, select {
  color: #ffffff !important;
}

input::placeholder, textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Button Text - Enhanced */
button {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Ensure all button text is visible */
button, button * {
  color: #ffffff !important;
}

/* Glass button text fixes */
.glass-button, .glass-button * {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Modern button text fixes */
.modern-button, .modern-button * {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Table Text */
table th, table td {
  color: rgba(255, 255, 255, 0.9);
}

/* Arabic Text Support - Enhanced */
.arabic-text, .font-arabic {
  font-family: 'Cairo', 'Tajawal', sans-serif !important;
  direction: rtl;
  text-align: right;
  font-weight: 500;
}

/* RTL Support - Enhanced */
[dir="rtl"] {
  direction: rtl !important;
  text-align: right !important;
}

[dir="ltr"] {
  direction: ltr !important;
  text-align: left !important;
}

/* Header RTL Support */
.glass-nav [dir="rtl"] {
  direction: rtl !important;
}

.glass-nav [dir="rtl"] .flex-row-reverse {
  flex-direction: row-reverse !important;
}

/* Header Logo Styles */
.header-logo {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
  transition: all 0.3s ease !important;
}

.header-logo:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4) !important;
}

/* Header Breadcrumb Styles */
.header-breadcrumb {
  color: rgba(255, 255, 255, 0.7) !important;
  transition: color 0.2s ease !important;
}

.header-breadcrumb.active {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Language Toggle Enhancements */
.language-toggle {
  min-width: 80px !important;
  justify-content: center !important;
}

.language-toggle .rtl-indicator {
  opacity: 0.6 !important;
  transition: opacity 0.2s ease !important;
}

.language-toggle:hover .rtl-indicator {
  opacity: 1 !important;
}

/* Arabic Font Improvements */
/* Font import moved to top of file */

.font-arabic {
  font-family: 'Cairo', 'Tajawal', sans-serif !important;
  font-weight: 500 !important;
  line-height: 1.6 !important;
}

/* RTL Layout Improvements */
[dir="rtl"] .flex-row-reverse {
  flex-direction: row-reverse !important;
}

[dir="rtl"] .text-right {
  text-align: right !important;
}

[dir="rtl"] .mr-4 {
  margin-right: 1rem !important;
  margin-left: 0 !important;
}

[dir="rtl"] .ml-4 {
  margin-left: 1rem !important;
  margin-right: 0 !important;
}

/* Header Navigation RTL Fixes */
.glass-nav [dir="rtl"] .breadcrumb-separator {
  transform: scaleX(-1) !important;
}

/* User Menu RTL Positioning */
.glass-nav [dir="rtl"] .user-menu {
  left: 0 !important;
  right: auto !important;
}

.glass-nav [dir="ltr"] .user-menu {
  right: 0 !important;
  left: auto !important;
}

/* Language Toggle Enhancements */
.language-toggle .language-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Footer RTL Support */
.footer-rtl {
  direction: rtl !important;
}

.footer-rtl .social-links {
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
}

/* Notification Badge RTL */
[dir="rtl"] .notification-badge {
  left: -4px !important;
  right: auto !important;
}

[dir="ltr"] .notification-badge {
  right: -4px !important;
  left: auto !important;
}

/* Fix Text Contrast Issues */
.modern-input, .modern-button {
  color: #ffffff !important;
}

.modern-input:focus {
  outline: none !important;
}

/* Card Text Fixes */
.modern-card h1, .modern-card h2, .modern-card h3, .modern-card h4, .modern-card h5, .modern-card h6 {
  color: #ffffff !important;
}

.modern-card p, .modern-card span, .modern-card div {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Table Text Visibility */
.modern-card table {
  color: rgba(255, 255, 255, 0.9) !important;
}

.modern-card table th {
  color: #ffffff !important;
  font-weight: 600;
}

.modern-card table td {
  color: rgba(255, 255, 255, 0.85) !important;
}

/* Button Text Visibility */
.modern-button, .modern-card button {
  color: #ffffff !important;
}

/* Glass Button Styles - Enhanced (consolidated) */
/* Styles moved to main glass-button section above */

/* Duplicate glass-button styles removed - consolidated above */

/* Glass Input Styles - Enhanced (consolidated with main section) */
/* Duplicate styles removed - see main glass-input section above */

/* KPI Component Specific Styles */
.kpi-card {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37) !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
}

.kpi-card:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.45) !important;
}

/* KPI Modal Styles */
.kpi-modal {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 16px !important;
  box-shadow: 0 16px 48px 0 rgba(31, 38, 135, 0.4) !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 70 !important; /* Higher z-index for modal content */
  max-height: 90vh !important;
  overflow-y: auto !important;
  width: 90vw !important;
  max-width: 600px !important;
}

/* Modal Backdrop */
.modal-backdrop {
  position: fixed !important;
  inset: 0 !important;
  z-index: 60 !important; /* Modal backdrop layer */
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Progress Bar Styles */
.kpi-progress-bar {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 9999px !important;
  overflow: hidden !important;
}

.kpi-progress-fill {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%) !important;
  border-radius: 9999px !important;
  transition: width 1s ease !important;
}

/* Badge Styles for KPI */
.kpi-badge {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 6px !important;
  color: #ffffff !important;
  font-weight: 500 !important;
  padding: 0.25rem 0.75rem !important;
}

/* Dropdown and Select Styles */
.glass-dropdown {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5) !important;
  color: #ffffff !important;
}

.glass-dropdown-item {
  color: #ffffff !important;
  transition: all 0.2s ease !important;
  padding: 0.5rem 0.75rem !important;
}

.glass-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

/* Select Trigger Styles */
.glass-select-trigger {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  border-radius: 8px !important;
}

.glass-select-trigger:focus {
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3) !important;
}

/* Tab Styles */
.glass-tabs {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  padding: 0.25rem !important;
}

.glass-tab {
  color: rgba(255, 255, 255, 0.7) !important;
  transition: all 0.2s ease !important;
  border-radius: 6px !important;
  padding: 0.5rem 1rem !important;
}

.glass-tab[data-state="active"] {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

/* Shadcn/UI Component Overrides for Glass Theme */

/* Button Overrides */
[data-slot="button"].glass-button {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.2) !important;
}

[data-slot="button"].glass-button:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 24px 0 rgba(31, 38, 135, 0.3) !important;
}

/* Select Component Overrides */
[data-radix-select-trigger].glass-input {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

[data-radix-select-trigger].glass-input:focus {
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3) !important;
}

[data-radix-select-content].glass-card {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

[data-radix-select-item] {
  color: #ffffff !important;
}

[data-radix-select-item]:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

/* Dialog Overrides */
[data-radix-dialog-content].glass-card {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 50 !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
}

/* Dialog Overlay */
[data-radix-dialog-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 60 !important; /* Dialog overlay layer */
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Dialog Portal */
[data-radix-dialog-portal] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 65 !important; /* Dialog portal layer - between overlay and content */
}

/* Dropdown Menu Overrides */
[data-radix-dropdown-menu-content].glass-card {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  z-index: 50 !important; /* Dropdown layer */
}

[data-radix-dropdown-menu-item] {
  color: #ffffff !important;
}

[data-radix-dropdown-menu-item]:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

/* Text and Typography Fixes */
.glass-card h1, .glass-card h2, .glass-card h3, .glass-card h4, .glass-card h5, .glass-card h6 {
  color: #ffffff !important;
}

.glass-card p, .glass-card span, .glass-card div {
  color: rgba(255, 255, 255, 0.9) !important;
}

.glass-card label {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
}

/* Badge and Status Overrides */
.glass-card .badge, .glass-card [data-slot="badge"] {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Progress Bar Overrides */
.glass-card [data-slot="progress"] {
  background: rgba(255, 255, 255, 0.2) !important;
}

.glass-card [data-slot="progress"] > div {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%) !important;
}

/* Tab Overrides */
[data-radix-tabs-list].glass-card {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

[data-radix-tabs-trigger] {
  color: rgba(255, 255, 255, 0.7) !important;
}

[data-radix-tabs-trigger][data-state="active"] {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

/* Input and Textarea Overrides */
input.glass-input, textarea.glass-input {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

input.glass-input:focus, textarea.glass-input:focus {
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3) !important;
}

/* Ensure all text is visible */
* {
  color: inherit;
}

/* Override any potential dark text */
.text-black, .text-gray-900, .text-gray-800 {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Force button text visibility - Universal Fix */
button[type="submit"],
button[type="button"],
button,
.btn,
[role="button"] {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Force button content visibility */
button *,
.btn *,
[role="button"] * {
  color: #ffffff !important;
}

/* Login button specific fixes */
.glass-button.bg-gradient-to-r,
.glass-button.bg-gradient-to-r *,
button.glass-button,
button.glass-button * {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Gradient button text fixes */
.bg-gradient-to-r,
.bg-gradient-to-r *,
.bg-gradient-to-l,
.bg-gradient-to-l *,
.bg-gradient-to-t,
.bg-gradient-to-t *,
.bg-gradient-to-b,
.bg-gradient-to-b * {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Universal Modal Centering Fix */
[role="dialog"], [data-radix-dialog-content], .modal, .dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 70 !important; /* Modal content layer */
}

/* Ensure modal backdrop covers full screen */
[data-radix-dialog-overlay], .modal-overlay, .dialog-overlay {
  position: fixed !important;
  inset: 0 !important;
  z-index: 60 !important; /* Modal overlay layer */
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Force center positioning for all modals */
.glass-card[role="dialog"] {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  z-index: 70 !important; /* Modal content layer */
}

/* Additional modal centering fixes */
.kpi-modal {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  z-index: 50 !important;
}

/* Radix Dialog specific fixes */
[data-radix-dialog-content] {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
  z-index: 50 !important;
}

/* Override any positioning from Tailwind or other libraries */
.fixed.inset-0 {
  position: fixed !important;
  inset: 0 !important;
}

/* Ensure dialog content is always centered */
[data-state="open"][role="dialog"] {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

/* Dashboard Layout Fixes */
.dashboard-container {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure main content uses full width */
main {
  width: 100% !important;
  max-width: none !important;
}

/* Dashboard title and content full width */
.dashboard-title {
  width: 100% !important;
  max-width: none !important;
}

/* Grid containers should use full width */
.dashboard-grid {
  width: 100% !important;
  max-width: none !important;
  margin: 0 !important;
}

/* Layout container fixes */
.layout-container {
  width: 100% !important;
  max-width: none !important;
}

/* Responsive layout adjustments */
@media (min-width: 1024px) {
  .dashboard-container {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .dashboard-grid {
    width: 100% !important;
    max-width: none !important;
  }

  /* Ensure stats grid uses full width on large screens */
  .dashboard-grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr)) !important;
  }
}

/* Fix for flex containers */
.flex-1 {
  width: 100% !important;
  max-width: none !important;
}

/* Force full width for all dashboard elements */
.dashboard-container,
.dashboard-container > *,
.dashboard-title,
.dashboard-grid {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Remove any margin constraints */
.dashboard-container {
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Ensure main layout uses full width */
main,
main > div {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}

/* Force grid containers to use full width */
.grid {
  width: 100% !important;
  max-width: none !important;
}

/* Override any Tailwind width constraints */
.w-full {
  width: 100% !important;
  max-width: none !important;
}

/* Aggressive full-width enforcement for dashboard */
body .dashboard-container,
body .dashboard-title,
body .dashboard-grid,
body main,
body main > div {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Force layout container to use full width */
.relative.z-10.flex.h-screen > div:last-child {
  width: 100% !important;
  max-width: none !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Remove any container constraints */
.container,
.max-w-7xl,
.max-w-6xl,
.max-w-5xl,
.max-w-4xl,
.max-w-3xl,
.max-w-2xl,
.max-w-xl,
.max-w-lg,
.max-w-md,
.max-w-sm {
  max-width: none !important;
  width: 100% !important;
}

/* Sidebar Animation Fixes */
.sidebar-container {
  transition: transform 0.3s ease-in-out !important;
  will-change: transform !important;
  z-index: 50 !important; /* Higher z-index for proper layering */
  pointer-events: auto !important; /* Ensure pointer events work */
  position: fixed !important;
  top: 0 !important;
  height: 100vh !important;
  width: 320px !important; /* Fixed width */
  overflow: hidden !important;
}

.sidebar-open {
  transform: translateX(0) !important;
  pointer-events: auto !important;
}

.sidebar-closed-ltr {
  transform: translateX(-100%) !important;
  pointer-events: none !important;
}

.sidebar-closed-rtl {
  transform: translateX(100%) !important;
  pointer-events: none !important;
}

/* Mobile-specific sidebar fixes */
@media (max-width: 768px) {
  .sidebar-container {
    z-index: 50 !important; /* Higher z-index for mobile */
    touch-action: pan-y !important;
    width: 280px !important; /* Slightly smaller on mobile */
  }

  .sidebar-open {
    transform: translateX(0) !important;
    pointer-events: auto !important;
  }

  .sidebar-closed-ltr {
    transform: translateX(-100%) !important;
    pointer-events: none !important;
  }

  .sidebar-closed-rtl {
    transform: translateX(100%) !important;
    pointer-events: none !important;
  }

  /* Improve touch targets on mobile */
  .sidebar-container button,
  .sidebar-container a {
    min-height: 44px !important;
    touch-action: manipulation !important;
  }
}

/* Sidebar navigation area fixes */
.sidebar-container nav {
  height: calc(100vh - 128px) !important; /* Account for header and footer */
  overflow-y: auto !important;
  overflow-x: hidden !important;
  scrollbar-width: thin !important;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent !important;
}

.sidebar-container nav::-webkit-scrollbar {
  width: 4px !important;
}

.sidebar-container nav::-webkit-scrollbar-track {
  background: transparent !important;
}

.sidebar-container nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 2px !important;
}

.sidebar-container nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5) !important;
}

/* Ensure sidebar buttons are clickable */
.sidebar-container button,
.sidebar-container a {
  position: relative !important;
  z-index: 51 !important;
  pointer-events: auto !important;
}

  /* Prevent text selection on mobile navigation */
  .sidebar-container {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }

/* Overlay styles - Removed old sidebar-overlay class */

/* Enhanced Mobile-First Responsive Design */

/* KPI Mobile Responsiveness Fixes */
@media (max-width: 768px) {
  /* KPI Management Page */
  .kpi-management-container {
    padding: 1rem !important;
    gap: 1rem !important;
  }

  /* KPI Grid Responsive */
  .kpi-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* KPI Cards Mobile */
  .kpi-card {
    margin-bottom: 1rem !important;
    min-height: auto !important;
  }

  .kpi-card .card-header {
    padding: 1rem !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .kpi-card .card-content {
    padding: 1rem !important;
  }

  /* KPI Card Grid Layout */
  .kpi-card .grid {
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
  }

  .kpi-card .grid-cols-2 {
    grid-template-columns: 1fr 1fr !important;
  }

  /* KPI Values Mobile */
  .kpi-value {
    font-size: 1.25rem !important;
    line-height: 1.5 !important;
  }

  /* KPI Progress Bar Mobile */
  .kpi-progress-bar {
    height: 6px !important;
    margin: 0.5rem 0 !important;
  }

  /* KPI Modal Mobile */
  .kpi-modal, [data-radix-dialog-content].glass-card {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    width: calc(100vw - 2rem) !important;
    max-width: calc(100vw - 2rem) !important;
    max-height: calc(100vh - 4rem) !important;
    border-radius: 12px !important;
    z-index: 70 !important; /* Consistent with desktop modal content */
    overflow-y: auto !important;
  }

  .kpi-modal .dialog-content {
    padding: 1rem !important;
  }

  .kpi-modal .grid-cols-2 {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .kpi-modal .grid-cols-3 {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* KPI Form Elements Mobile */
  .glass-input, .glass-button {
    min-height: 44px !important;
    font-size: 16px !important;
    padding: 0.75rem !important;
  }

  /* KPI Tabs Mobile */
  .glass-tabs {
    flex-direction: column !important;
  }

  .glass-tab {
    width: 100% !important;
    justify-content: center !important;
    padding: 0.75rem !important;
  }

  /* KPI Header Mobile */
  .kpi-header {
    flex-direction: column !important;
    gap: 1rem !important;
    text-align: center !important;
  }

  .kpi-header h1 {
    font-size: 1.75rem !important;
    line-height: 1.2 !important;
  }

  /* KPI Actions Mobile */
  .kpi-actions {
    flex-direction: column !important;
    width: 100% !important;
    gap: 0.5rem !important;
  }

  .kpi-actions .glass-button {
    width: 100% !important;
    justify-content: center !important;
  }

  /* KPI Dropdown Mobile */
  .kpi-dropdown {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    top: auto !important;
    border-radius: 1rem 1rem 0 0 !important;
    max-height: 50vh !important;
  }

  /* KPI Stats Mobile */
  .kpi-stats {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .kpi-stat-card {
    padding: 1rem !important;
  }

  /* KPI Chart Mobile */
  .kpi-chart {
    height: 200px !important;
    margin: 1rem 0 !important;
  }
}

/* Tablet Responsiveness */
@media (min-width: 769px) and (max-width: 1024px) {
  .kpi-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1.5rem !important;
  }

  .kpi-modal, [data-radix-dialog-content].glass-card {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90vw !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
  }

  .kpi-modal .grid-cols-3 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .kpi-header {
    flex-direction: row !important;
    flex-wrap: wrap !important;
  }

  .kpi-actions {
    flex-direction: row !important;
    flex-wrap: wrap !important;
  }
}

/* Large Screen Optimizations */
@media (min-width: 1025px) {
  .kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
    gap: 2rem !important;
  }

  .kpi-modal, [data-radix-dialog-content].glass-card {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: auto !important;
    max-width: 800px !important;
    max-height: 90vh !important;
    min-width: 600px !important;
  }

  .kpi-card:hover {
    transform: translateY(-4px) !important;
  }
}

/* Touch Device Improvements */
@media (hover: none) and (pointer: coarse) {
  .glass-button {
    min-height: 48px !important;
    font-size: 16px !important;
    padding: 0.875rem 1.5rem !important;
  }

  .glass-input {
    min-height: 48px !important;
    font-size: 16px !important;
    padding: 0.875rem !important;
  }

  .kpi-card {
    padding: 1rem !important;
  }

  .kpi-card:hover {
    transform: none !important;
  }

  .kpi-dropdown-item {
    min-height: 48px !important;
    padding: 0.875rem !important;
  }
}

/* Mobile Touch Improvements */
@media (hover: none) and (pointer: coarse) {
  .modern-button, .glass-button {
    min-height: 44px; /* iOS recommended touch target */
    min-width: 44px;
    padding: 12px 16px;
  }

  .modern-input {
    min-height: 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Larger touch targets for mobile */
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Mobile-Specific Animations */
@media (prefers-reduced-motion: no-preference) {
  .mobile-slide-in {
    animation: mobileSlideIn 0.3s ease-out;
  }

  .mobile-fade-in {
    animation: mobileFadeIn 0.2s ease-out;
  }
}

@keyframes mobileSlideIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes mobileFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive Typography */
@media (max-width: 768px) {
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }

  .text-6xl { font-size: 2.5rem; }
  .text-5xl { font-size: 2rem; }
  .text-4xl { font-size: 1.75rem; }
  .text-3xl { font-size: 1.5rem; }
  .text-2xl { font-size: 1.25rem; }
}

/* Mobile Navigation Improvements */
.mobile-nav-item {
  padding: 12px 16px;
  min-height: 48px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.mobile-nav-item:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.98);
}

/* Swipe Gesture Indicators */
.swipe-indicator {
  position: relative;
}

.swipe-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -4px;
  width: 4px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.swipe-indicator:hover::before {
  opacity: 1;
}

/* Enhanced Glass Effects for Mobile */
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: 12px;
  }

  .modern-card {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
  }

  /* Modal responsiveness fixes */
  [data-radix-dialog-content] {
    width: 95vw !important;
    max-width: 95vw !important;
    max-height: 95vh !important;
    margin: 0 !important;
    padding: 1rem !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Form field spacing on mobile */
  .grid[class*="grid-cols"] {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* Button spacing on mobile */
  .flex.flex-col.sm\\:flex-row {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  /* Scrollable form container on mobile */
  .overflow-y-auto[class*="max-h-"] {
    max-height: calc(95vh - 150px) !important;
  }
}

/* Pull-to-Refresh Indicator */
.pull-to-refresh {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0 0 12px 12px;
  color: white;
  font-size: 14px;
  opacity: 0;
  transition: all 0.3s ease;
}

.pull-to-refresh.active {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* Mobile Bottom Sheet */
.bottom-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px 20px 0 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.bottom-sheet.open {
  transform: translateY(0);
}

.bottom-sheet-handle {
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  margin: 12px auto 20px;
}

/* Enhanced Scroll Behavior */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Mobile-Optimized Loading States */
.mobile-skeleton {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
  background-size: 200% 100%;
  animation: mobileShimmer 1.5s infinite;
}

@keyframes mobileShimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Haptic Feedback Simulation */
.haptic-light {
  animation: hapticLight 0.1s ease;
}

.haptic-medium {
  animation: hapticMedium 0.15s ease;
}

.haptic-heavy {
  animation: hapticHeavy 0.2s ease;
}

@keyframes hapticLight {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes hapticMedium {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes hapticHeavy {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.08); }
}

/* Safe Area Support for iOS */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(16px, env(safe-area-inset-top));
  }

  .safe-area-bottom {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }

  .safe-area-left {
    padding-left: max(16px, env(safe-area-inset-left));
  }

  .safe-area-right {
    padding-right: max(16px, env(safe-area-inset-right));
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }

  .auto-dark .glass-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-card, .modern-card {
    border: 2px solid rgba(255, 255, 255, 0.5);
    background: rgba(0, 0, 0, 0.8);
  }

  .modern-button {
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .floating, .animate-bounce-slow, .animate-rotate-slow {
    animation: none;
  }
}

/* KPI Specific Fixes */
.kpi-management-container {
  width: 100% !important;
  max-width: none !important;
  padding: 1rem !important;
}

/* KPI Grid Fixes */
.kpi-grid {
  width: 100% !important;
  max-width: none !important;
  display: grid !important;
  gap: 1rem !important;
}

/* KPI Card Fixes */
.kpi-card {
  width: 100% !important;
  max-width: none !important;
  min-height: auto !important;
  overflow: hidden !important;
}

/* KPI Modal Overlay Fix */
[data-radix-dialog-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 60 !important; /* Consistent overlay layer */
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
}

/* KPI Form Fixes */
.kpi-form {
  width: 100% !important;
  max-width: none !important;
}

.kpi-form .grid {
  width: 100% !important;
  gap: 1rem !important;
}

/* KPI Tabs Fixes */
.kpi-tabs {
  width: 100% !important;
  max-width: none !important;
}

.kpi-tabs [role="tablist"] {
  width: 100% !important;
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)) !important;
}

/* KPI Button Fixes */
.kpi-button {
  min-height: 44px !important;
  padding: 0.75rem 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
}

/* KPI Input Fixes */
.kpi-input {
  min-height: 44px !important;
  padding: 0.75rem !important;
  width: 100% !important;
  font-size: 16px !important;
}

/* KPI Select Fixes */
.kpi-select {
  min-height: 44px !important;
  width: 100% !important;
}

/* KPI Progress Bar Fixes */
.kpi-progress {
  width: 100% !important;
  height: 8px !important;
  border-radius: 4px !important;
  overflow: hidden !important;
}

.kpi-progress-fill {
  height: 100% !important;
  transition: width 0.3s ease !important;
}

/* KPI Alert Fixes */
.kpi-alert {
  width: 100% !important;
  padding: 1rem !important;
  border-radius: 8px !important;
  margin-bottom: 1rem !important;
}

/* KPI Chart Fixes */
.kpi-chart {
  width: 100% !important;
  height: 300px !important;
  min-height: 200px !important;
}

/* Extra Small Breakpoint (xs) */
@media (min-width: 475px) {
  .xs\:inline {
    display: inline !important;
  }

  .xs\:flex-row {
    flex-direction: row !important;
  }

  .xs\:w-auto {
    width: auto !important;
  }

  .xs\:items-center {
    align-items: center !important;
  }
}

/* KPI Management Responsive Fixes */
@media (max-width: 640px) {
  /* Mobile tabs */
  .kpi-tabs [role="tablist"] {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.25rem !important;
  }

  .kpi-tabs [role="tab"] {
    padding: 0.5rem 0.25rem !important;
    font-size: 0.75rem !important;
    min-height: 44px !important;
  }

  /* Mobile filters */
  .kpi-grid-container .grid {
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
  }

  /* Mobile buttons */
  .kpi-actions {
    width: 100% !important;
  }

  .kpi-actions .glass-button {
    width: 100% !important;
    justify-content: center !important;
    min-height: 44px !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* Tablet tabs */
  .kpi-tabs [role="tablist"] {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  /* Tablet filters */
  .kpi-grid-container .grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* Desktop small tabs */
  .kpi-tabs [role="tablist"] {
    grid-template-columns: repeat(5, 1fr) !important;
  }

  /* Desktop small filters */
  .kpi-grid-container .grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

/* KPI Card Responsive Fixes */
@media (max-width: 640px) {
  .kpi-card {
    margin-bottom: 1rem !important;
  }

  .kpi-card .card-header {
    padding: 1rem !important;
  }

  .kpi-card .card-content {
    padding: 1rem !important;
  }

  .kpi-value {
    font-size: 1.125rem !important;
  }
}

/* Tab Content Responsive */
.kpi-management-container .space-y-6 > * {
  margin-bottom: 1.5rem !important;
}

@media (max-width: 640px) {
  .kpi-management-container .space-y-6 > * {
    margin-bottom: 1rem !important;
  }
}

/* Label Text - Enhanced */
label, label *, [data-slot="label"], [data-slot="label"] * {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Form element labels */
.form-label, .field-label, .input-label {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
}

/* Link Text */
a {
  color: rgba(255, 255, 255, 0.9) !important;
}

a:hover {
  color: #ffffff !important;
}

/* Badge and Status Text */
.badge, .status {
  color: #ffffff !important;
  font-weight: 500;
}

/* Ensure all text elements are visible */
* {
  color: inherit;
}

/* Override any dark text that might be invisible */
.text-gray-900, .text-gray-800, .text-gray-700, .text-gray-600, .text-gray-500 {
  color: rgba(255, 255, 255, 0.9) !important;
}

.text-gray-400, .text-gray-300 {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* Ensure form elements are visible */
select option {
  background: rgba(0, 0, 0, 0.8) !important;
  color: #ffffff !important;
}

/* Dropdown and menu text */
.dropdown-menu, .menu {
  background: rgba(0, 0, 0, 0.8) !important;
  color: #ffffff !important;
}

/* Tooltip text */
.tooltip {
  background: rgba(0, 0, 0, 0.9) !important;
  color: #ffffff !important;
}

/* Login Form Specific Fixes */
.login-form button,
.login-form button *,
form button[type="submit"],
form button[type="submit"] * {
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Card title and description fixes */
.glass-card .card-title,
.glass-card .card-description,
.glass-card h1, .glass-card h2, .glass-card h3,
.glass-card p, .glass-card span {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Input placeholder fixes */
.glass-input::placeholder,
input::placeholder,
textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Demo accounts section fixes */
.demo-accounts, .demo-accounts * {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Code elements in demo section */
code {
  color: #7dd3fc !important;
  background: rgba(0, 0, 0, 0.3) !important;
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    background-attachment: fixed;
    color: #e2e8f0;
  }

  .glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .modern-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .modern-button {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .modern-input {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* ===== COMPREHENSIVE TEXT VISIBILITY FIXES ===== */
/* This section ensures ALL text is visible across the application */

/* Universal text color enforcement */
body, body * {
  color: inherit !important;
}

/* Ensure white text on dark backgrounds */
body {
  color: #ffffff !important;
}

/* Button text - absolute enforcement */
button,
.button,
[role="button"],
input[type="submit"],
input[type="button"] {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Button content - nested elements */
button *,
.button *,
[role="button"] *,
input[type="submit"] *,
input[type="button"] * {
  color: #ffffff !important;
}

/* Form elements */
label,
.label,
[data-slot="label"] {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
}

/* Input elements */
input,
textarea,
select {
  color: #ffffff !important;
}

/* Placeholder text */
input::placeholder,
textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Card content */
.card *,
.glass-card *,
.modern-card * {
  color: inherit !important;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  color: #ffffff !important;
  font-weight: 600 !important;
}

/* Paragraphs and text */
p, span, div {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Links */
a {
  color: rgba(255, 255, 255, 0.9) !important;
}

a:hover {
  color: #ffffff !important;
}

/* Loading text */
.loading-text, .spinner-text {
  color: #ffffff !important;
}

/* Error and success messages */
.error, .success, .warning, .info {
  color: #ffffff !important;
}

/* Dropdown and menu items */
.dropdown-item, .menu-item {
  color: #ffffff !important;
}

/* Badge and status indicators */
.badge, .status, .tag {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* Table content */
table, th, td {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Navigation items */
.nav-item, .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Modal content */
.modal *, .dialog * {
  color: inherit !important;
}

/* Tooltip content */
.tooltip, .tooltip * {
  color: #ffffff !important;
}

/* Code elements */
code, pre {
  color: #7dd3fc !important;
  background: rgba(0, 0, 0, 0.3) !important;
}

/* ===== LOGIN PAGE SPECIFIC FIXES ===== */
/* Target the login page specifically */

/* Login form container */
.login-container,
.login-container * {
  color: inherit !important;
}

/* Login button - most important fix */
.login-container button[type="submit"],
.login-container button[type="submit"] *,
.login-container .glass-button,
.login-container .glass-button *,
form button[type="submit"],
form button[type="submit"] * {
  color: #0f0e0e !important;
  font-weight: 600 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
  -webkit-text-fill-color: #ffffff !important;
}

/* Login form labels */
.login-container label,
.login-container .label,
.login-container [data-slot="label"] {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Login form inputs */
.login-container input,
.login-container .glass-input {
  color: #ffffff !important;
}

/* Login form placeholders */
.login-container input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Login card titles and descriptions */
.login-container .card-title,
.login-container .card-description,
.login-container h1,
.login-container h2,
.login-container h3,
.login-container p {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Demo accounts section */
.login-container .demo-accounts,
.login-container .demo-accounts * {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Demo credentials code */
.login-container code {
  color: #7dd3fc !important;
  background: rgba(0, 0, 0, 0.3) !important;
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
}

/* ===== FINAL OVERRIDE - NUCLEAR OPTION ===== */
/* If all else fails, this should work */

/* Force all text to be visible */
* {
  -webkit-text-fill-color: inherit !important;
}

/* Ensure no transparent text */
*:not(.bg-gradient-to-r):not(.bg-gradient-to-l):not(.bg-gradient-to-t):not(.bg-gradient-to-b) {
  -webkit-text-fill-color: unset !important;
}

/* Button text - final enforcement */
button:not(.bg-gradient-to-r):not(.bg-gradient-to-l):not(.bg-gradient-to-t):not(.bg-gradient-to-b),
button:not(.bg-gradient-to-r):not(.bg-gradient-to-l):not(.bg-gradient-to-t):not(.bg-gradient-to-b) * {
  -webkit-text-fill-color: #ffffff !important;
  color: #ffffff !important;
}

/* Gradient button text - special handling */
.bg-gradient-to-r, .bg-gradient-to-l, .bg-gradient-to-t, .bg-gradient-to-b {
  color: #ffffff !important;
}

.bg-gradient-to-r *, .bg-gradient-to-l *, .bg-gradient-to-t *, .bg-gradient-to-b * {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
}

/* Login button specific - most important */
button.glass-button.bg-gradient-to-r,
button.glass-button.bg-gradient-to-r *,
.login-container button[type="submit"],
.login-container button[type="submit"] * {
  color: #0e0d0d !important;
  -webkit-text-fill-color: #ffffff !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
  font-weight: 600 !important;
  opacity: 1 !important;
  visibility: visible !important;
}
