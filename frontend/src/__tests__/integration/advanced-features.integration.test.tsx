/**
 * Advanced Features Integration Tests
 * End-to-end integration tests for search, export, and notifications
 */

import React from 'react'
import { screen, waitFor, fireEvent } from '@testing-library/dom'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, createMockSearchResult, createMockNotification } from '../../test-utils/test-utils'
import { server } from '../../test-utils/mocks/server'
import { http } from 'msw'
import AdvancedFeaturesDemo from '../../pages/enhanced/AdvancedFeaturesDemo'

describe('Advanced Features Integration', () => {
  const mockInitialState = {
    auth: {
      user: {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        role: { id: 'employee', name: 'Employee', nameAr: 'موظف' },
      },
      token: 'mock-token',
      isAuthenticated: true,
      loading: false,
      error: null,
    },
    notifications: {
      notifications: [
        createMockNotification({
          id: '1',
          title: 'Test Notification',
          message: 'This is a test notification',
          read: false,
        }),
      ],
      unreadCount: 1,
      settings: {
        enableSound: true,
        enableDesktop: true,
        enableEmail: false,
        autoMarkRead: false,
        groupSimilar: true,
      },
    },
  }

  beforeEach(() => {
    // Reset MSW handlers
    server.resetHandlers()
  })

  describe('Search Integration', () => {
    it('should perform end-to-end search workflow', async () => {
      const user = userEvent.setup()

      // Mock search API response
      server.use(
        http.post('*/search/global', () => {
          return Response.json({
            results: [
              createMockSearchResult({
                title: 'John Doe',
                description: 'Software Engineer',
                type: 'employee',
              }),
            ],
            total: 1,
            page: 1,
            limit: 10,
            executionTime: 45,
          })
        })
      )

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Click the search button
      const searchButton = screen.getByText('Try Search')
      await user.click(searchButton)

      // Wait for search dialog to open
      await waitFor(() => {
        expect(screen.getByPlaceholderText(/search across the entire system/i)).toBeInTheDocument()
      })

      // Type search query
      const searchInput = screen.getByPlaceholderText(/search across the entire system/i)
      await user.type(searchInput, 'john')

      // Wait for search results
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Software Engineer')).toBeInTheDocument()
      })

      // Click on search result
      await user.click(screen.getByText('John Doe'))

      // Search dialog should close
      await waitFor(() => {
        expect(screen.queryByPlaceholderText(/search across the entire system/i)).not.toBeInTheDocument()
      })
    })

    it('should handle search suggestions', async () => {
      const user = userEvent.setup()

      // Mock suggestions API response
      server.use(
        http.get('*/search/suggestions', () => {
          return Response.json({
            suggestions: ['john doe', 'john smith', 'johnny walker'],
          })
        })
      )

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Open search
      await user.keyboard('{Control>}k{/Control}')

      await waitFor(() => {
        expect(screen.getByPlaceholderText(/search across the entire system/i)).toBeInTheDocument()
      })

      // Type partial query
      const searchInput = screen.getByPlaceholderText(/search across the entire system/i)
      await user.type(searchInput, 'joh')

      // Wait for suggestions
      await waitFor(() => {
        expect(screen.getByText('john doe')).toBeInTheDocument()
        expect(screen.getByText('john smith')).toBeInTheDocument()
      })

      // Click on suggestion
      await user.click(screen.getByText('john doe'))

      expect(searchInput).toHaveValue('john doe')
    })

    it('should handle search errors gracefully', async () => {
      const user = userEvent.setup()

      // Mock search API error
      server.use(
        http.post('*/search/global', () => {
          return new Response(JSON.stringify({ message: 'Search service unavailable' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          })
        })
      )

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Open search and type query
      await user.keyboard('{Control>}k{/Control}')

      const searchInput = await screen.findByPlaceholderText(/search across the entire system/i)
      await user.type(searchInput, 'test')

      // Should show no results instead of crashing
      await waitFor(() => {
        expect(screen.getByText('No results found')).toBeInTheDocument()
      })
    })
  })

  describe('Export Integration', () => {
    it('should perform end-to-end export workflow', async () => {
      const user = userEvent.setup()

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Click export button
      const exportButton = screen.getByText('Export Sample')
      await user.click(exportButton)

      // Wait for export dialog to open
      await waitFor(() => {
        expect(screen.getByText('Export Sample')).toBeInTheDocument()
        expect(screen.getByText('Format')).toBeInTheDocument()
      })

      // Select PDF format
      const pdfButton = screen.getByRole('button', { name: /pdf/i })
      await user.click(pdfButton)

      // Modify filename
      const filenameInput = screen.getByLabelText(/filename/i)
      await user.clear(filenameInput)
      await user.type(filenameInput, 'custom_export')

      // Deselect a column
      const columnCheckboxes = screen.getAllByRole('checkbox')
      if (columnCheckboxes.length > 0) {
        await user.click(columnCheckboxes[0])
      }

      // Start export
      const exportActionButton = screen.getByRole('button', { name: /export/i })
      await user.click(exportActionButton)

      // Should show export progress
      await waitFor(() => {
        expect(screen.getByText(/processing/i)).toBeInTheDocument()
      })

      // Should complete export
      await waitFor(() => {
        expect(screen.getByText(/export complete/i)).toBeInTheDocument()
      }, { timeout: 5000 })
    })

    it('should handle template saving', async () => {
      const user = userEvent.setup()

      // Mock template save API
      server.use(
        http.post('*/export/templates', () => {
          return Response.json({
            template: {
              id: 'new-template-id',
              name: 'Custom Template',
              description: 'demo export template',
              format: 'excel',
              columns: [],
              filters: {},
            },
          })
        })
      )

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Open export dialog
      await user.click(screen.getByText('Export Sample'))

      await waitFor(() => {
        expect(screen.getByText('Export Sample')).toBeInTheDocument()
      })

      // Click save template
      const saveTemplateButton = screen.getByRole('button', { name: /save as template/i })
      await user.click(saveTemplateButton)

      // Enter template name
      const templateNameInput = screen.getByPlaceholderText(/template name/i)
      await user.type(templateNameInput, 'Custom Template')

      // Save template
      const saveButton = screen.getByRole('button', { name: '' }) // Check mark button
      await user.click(saveButton)

      // Template input should disappear
      await waitFor(() => {
        expect(screen.queryByPlaceholderText(/template name/i)).not.toBeInTheDocument()
      })
    })
  })

  describe('Notification Integration', () => {
    it('should display and manage notifications', async () => {
      const user = userEvent.setup()

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Open notifications
      await user.keyboard('{Control>}n{/Control}')

      await waitFor(() => {
        expect(screen.getByText('Notifications')).toBeInTheDocument()
        expect(screen.getByText('Test Notification')).toBeInTheDocument()
      })

      // Filter by unread
      await user.click(screen.getByText('Unread'))

      // Should still show the unread notification
      expect(screen.getByText('Test Notification')).toBeInTheDocument()

      // Mark as read
      const markReadButtons = screen.getAllByRole('button', { name: '' })
      const markReadButton = markReadButtons.find(button =>
        button.querySelector('svg') &&
        button.closest('div')?.textContent?.includes('Test Notification')
      )

      if (markReadButton) {
        await user.click(markReadButton)
      }

      // Search notifications
      const searchInput = screen.getByPlaceholderText(/search notifications/i)
      await user.type(searchInput, 'test')

      expect(screen.getByText('Test Notification')).toBeInTheDocument()
    })

    it('should handle notification actions', async () => {
      const user = userEvent.setup()

      const notificationWithActions = createMockNotification({
        id: '2',
        title: 'Task Assignment',
        message: 'New task assigned',
        actions: [
          { label: 'View Task', action: 'view_task' },
          { label: 'Accept', action: 'accept_task' },
        ],
      })

      const stateWithActions = {
        ...mockInitialState,
        notifications: {
          ...mockInitialState.notifications,
          notifications: [notificationWithActions],
        },
      }

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: stateWithActions,
      })

      // Open notifications
      await user.keyboard('{Control>}n{/Control}')

      await waitFor(() => {
        expect(screen.getByText('Task Assignment')).toBeInTheDocument()
        expect(screen.getByText('View Task')).toBeInTheDocument()
        expect(screen.getByText('Accept')).toBeInTheDocument()
      })

      // Click on action button
      await user.click(screen.getByText('View Task'))

      // Action should be handled (in real app, would navigate or perform action)
    })
  })

  describe('Keyboard Shortcuts Integration', () => {
    it('should handle all keyboard shortcuts', async () => {
      const user = userEvent.setup()

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Test search shortcut
      await user.keyboard('{Control>}k{/Control}')
      await waitFor(() => {
        expect(screen.getByPlaceholderText(/search across the entire system/i)).toBeInTheDocument()
      })

      // Close with Escape
      await user.keyboard('{Escape}')
      await waitFor(() => {
        expect(screen.queryByPlaceholderText(/search across the entire system/i)).not.toBeInTheDocument()
      })

      // Test export shortcut
      await user.keyboard('{Control>}e{/Control}')
      await waitFor(() => {
        expect(screen.getByText('Export Data')).toBeInTheDocument()
      })

      // Close with Escape
      await user.keyboard('{Escape}')
      await waitFor(() => {
        expect(screen.queryByText('Export Data')).not.toBeInTheDocument()
      })

      // Test notifications shortcut
      await user.keyboard('{Control>}n{/Control}')
      await waitFor(() => {
        expect(screen.getByText('Notifications')).toBeInTheDocument()
      })

      // Close with Escape
      await user.keyboard('{Escape}')
      await waitFor(() => {
        expect(screen.queryByText('Notifications')).not.toBeInTheDocument()
      })
    })

    it('should handle Mac shortcuts', async () => {
      const user = userEvent.setup()

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Test Cmd+K on Mac
      await user.keyboard('{Meta>}k{/Meta}')
      await waitFor(() => {
        expect(screen.getByPlaceholderText(/search across the entire system/i)).toBeInTheDocument()
      })
    })
  })

  describe('Multi-language Integration', () => {
    it('should work correctly in Arabic', async () => {
      const user = userEvent.setup()

      renderWithProviders(<AdvancedFeaturesDemo language="ar" />, {
        initialState: mockInitialState,
      })

      // Test search in Arabic
      await user.keyboard('{Control>}k{/Control}')
      await waitFor(() => {
        expect(screen.getByPlaceholderText(/ابحث في جميع أنحاء النظام/)).toBeInTheDocument()
      })

      // Test export in Arabic
      await user.keyboard('{Escape}')
      await user.click(screen.getByText('تصدير عينة'))

      await waitFor(() => {
        expect(screen.getByText('تصدير البيانات')).toBeInTheDocument()
      })

      // Test notifications in Arabic
      await user.keyboard('{Escape}')
      await user.keyboard('{Control>}n{/Control}')

      await waitFor(() => {
        expect(screen.getByText('الإشعارات')).toBeInTheDocument()
      })
    })
  })

  describe('Error Recovery Integration', () => {
    it('should recover from network errors', async () => {
      const user = userEvent.setup()

      // Mock network error then recovery
      let callCount = 0
      server.use(
        http.post('*/search/global', () => {
          callCount++
          if (callCount === 1) {
            return new Response(JSON.stringify({ message: 'Network error' }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            })
          }
          return Response.json({
            results: [createMockSearchResult({ title: 'Recovered Result' })],
            total: 1,
            page: 1,
            limit: 10,
            executionTime: 30,
          })
        })
      )

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Open search and try query (will fail)
      await user.keyboard('{Control>}k{/Control}')
      const searchInput = await screen.findByPlaceholderText(/search across the entire system/i)
      await user.type(searchInput, 'test')

      // Should show no results
      await waitFor(() => {
        expect(screen.getByText('No results found')).toBeInTheDocument()
      })

      // Clear and try again (will succeed)
      await user.clear(searchInput)
      await user.type(searchInput, 'retry')

      // Should show recovered result
      await waitFor(() => {
        expect(screen.getByText('Recovered Result')).toBeInTheDocument()
      })
    })
  })

  describe('Performance Integration', () => {
    it('should handle rapid user interactions', async () => {
      const user = userEvent.setup()

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Rapidly open and close dialogs
      for (let i = 0; i < 5; i++) {
        await user.keyboard('{Control>}k{/Control}')
        await user.keyboard('{Escape}')
        await user.keyboard('{Control>}e{/Control}')
        await user.keyboard('{Escape}')
        await user.keyboard('{Control>}n{/Control}')
        await user.keyboard('{Escape}')
      }

      // Should still be functional
      await user.keyboard('{Control>}k{/Control}')
      await waitFor(() => {
        expect(screen.getByPlaceholderText(/search across the entire system/i)).toBeInTheDocument()
      })
    })

    it('should handle large datasets efficiently', async () => {
      const user = userEvent.setup()

      // Mock large search results
      const largeResults = Array.from({ length: 100 }, (_, i) =>
        createMockSearchResult({
          id: `result-${i}`,
          title: `Result ${i}`,
          description: `Description for result ${i}`,
        })
      )

      server.use(
        http.post('*/search/global', () => {
          return Response.json({
            results: largeResults,
            total: 100,
            page: 1,
            limit: 10,
            executionTime: 150,
          })
        })
      )

      renderWithProviders(<AdvancedFeaturesDemo language="en" />, {
        initialState: mockInitialState,
      })

      // Perform search
      await user.keyboard('{Control>}k{/Control}')
      const searchInput = await screen.findByPlaceholderText(/search across the entire system/i)
      await user.type(searchInput, 'large dataset')

      // Should handle large results without performance issues
      await waitFor(() => {
        expect(screen.getByText('Result 0')).toBeInTheDocument()
      })

      // Should be able to scroll through results
      const resultsContainer = screen.getByText('Result 0').closest('div')
      expect(resultsContainer).toBeInTheDocument()
    })
  })
})
