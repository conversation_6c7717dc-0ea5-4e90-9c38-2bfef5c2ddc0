import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { KPI, KPICategory } from '@/services/kpiService'
import KPIDetailView from './KPIDetailView'
import { validateForm, kpiValidationSchema } from '@/utils/validation'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, CheckCircle } from 'lucide-react'

interface KPIModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (kpiData: Partial<KPI>) => Promise<void>
  kpi?: KPI | null
  categories: KPICategory[]
  language: 'ar' | 'en'
  mode?: 'create' | 'edit' | 'view'
}

const translations = {
  ar: {
    createKPI: 'إنشاء مؤشر أداء جديد',
    editKPI: 'تعديل مؤشر الأداء',
    name: 'الاسم',
    nameAr: 'الاسم بالعربية',
    description: 'الوصف',
    descriptionAr: 'الوصف بالعربية',
    category: 'الفئة',
    measurementType: 'نوع القياس',
    unit: 'الوحدة',
    frequency: 'التكرار',
    targetValue: 'القيمة المستهدفة',
    warningThreshold: 'عتبة التحذير',
    criticalThreshold: 'العتبة الحرجة',
    formula: 'الصيغة',
    dataSource: 'مصدر البيانات',
    save: 'حفظ',
    cancel: 'إلغاء',
    saving: 'جاري الحفظ...'
  },
  en: {
    createKPI: 'Create New KPI',
    editKPI: 'Edit KPI',
    name: 'Name',
    nameAr: 'Name (Arabic)',
    description: 'Description',
    descriptionAr: 'Description (Arabic)',
    category: 'Category',
    measurementType: 'Measurement Type',
    unit: 'Unit',
    frequency: 'Frequency',
    targetValue: 'Target Value',
    warningThreshold: 'Warning Threshold',
    criticalThreshold: 'Critical Threshold',
    formula: 'Formula',
    dataSource: 'Data Source',
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...'
  }
}

export default function KPIModal({ isOpen, onClose, onSave, kpi, categories, language, mode = 'create' }: KPIModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    name_ar: '',
    description: '',
    description_ar: '',
    category: '',
    measurement_type: 'NUMBER',
    unit: '',
    unit_ar: '',
    frequency: 'MONTHLY',
    target_value: 0,
    warning_threshold: 0,
    critical_threshold: 0,
    formula: '',
    data_source: '',
    trend_direction: 'UP'
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  useEffect(() => {
    if (kpi) {
      setFormData({
        name: kpi.name || '',
        name_ar: kpi.name_ar || '',
        description: kpi.description || '',
        description_ar: kpi.description_ar || '',
        category: kpi.category || '',
        measurement_type: kpi.measurement_type || 'NUMBER',
        unit: kpi.unit || '',
        unit_ar: kpi.unit_ar || '',
        frequency: kpi.frequency || 'MONTHLY',
        target_value: kpi.target_value || 0,
        warning_threshold: kpi.warning_threshold || 0,
        critical_threshold: kpi.critical_threshold || 0,
        formula: kpi.formula || '',
        data_source: kpi.data_source || '',
        trend_direction: kpi.trend_direction || 'UP'
      })
    } else {
      // Reset form for new KPI
      setFormData({
        name: '',
        name_ar: '',
        description: '',
        description_ar: '',
        category: '',
        measurement_type: 'NUMBER',
        unit: '',
        unit_ar: '',
        frequency: 'MONTHLY',
        target_value: 0,
        warning_threshold: 0,
        critical_threshold: 0,
        formula: '',
        data_source: '',
        trend_direction: 'UP'
      })
    }
  }, [kpi, isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setSubmitError(null)
    setSubmitSuccess(false)

    try {
      // Validate form data
      const validationErrors = validateForm(formData, kpiValidationSchema)

      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors)
        setLoading(false)
        return
      }

      // Clear any previous errors
      setErrors({})

      // Submit the form
      await onSave(formData as any)
      setSubmitSuccess(true)

      // Close modal after a brief success message
      setTimeout(() => {
        onClose()
        setSubmitSuccess(false)
      }, 1500)
    } catch (error: any) {
      console.error('Error saving KPI:', error)
      setSubmitError(
        error.response?.data?.message ||
        error.message ||
        'An error occurred while saving the KPI'
      )
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string | number) => {
    const updatedFormData = {
      ...formData,
      [field]: value
    }

    setFormData(updatedFormData)

    // Clear error for this field when user makes changes
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }

    // Validate related fields
    if (field === 'target_value' && (errors.warning_threshold || errors.critical_threshold)) {
      // Re-validate thresholds when target changes
      const thresholdErrors = validateForm(
        updatedFormData,
        {
          warning_threshold: kpiValidationSchema.warning_threshold,
          critical_threshold: kpiValidationSchema.critical_threshold
        }
      )

      setErrors(prev => ({
        ...prev,
        ...thresholdErrors
      }))
    }
  }

  // If in view mode and we have a KPI, render the detail view
  if (mode === 'view' && kpi) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className={`kpi-modal glass-card border-white/20 max-w-6xl max-h-[95vh] overflow-y-auto ${isRTL ? 'rtl' : 'ltr'}`}>
          <KPIDetailView kpi={kpi} language={language} onClose={onClose} />
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`kpi-modal kpi-form glass-card border-white/20 max-w-2xl max-h-[90vh] overflow-y-auto ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <DialogTitle className="text-white text-xl">
            {mode === 'edit' ? t.editKPI : t.createKPI}
          </DialogTitle>
          <DialogDescription className="text-white/70">
            {language === 'ar' ? 'املأ النموذج أدناه لحفظ مؤشر الأداء' : 'Fill out the form below to save the KPI'}
          </DialogDescription>
        </DialogHeader>

        {/* Success Message */}
        {submitSuccess && (
          <Alert className="border-green-500/50 bg-green-500/10">
            <CheckCircle className="h-4 w-4 text-green-400" />
            <AlertTitle className="text-green-400">
              {language === 'ar' ? 'تم الحفظ بنجاح' : 'Success'}
            </AlertTitle>
            <AlertDescription className="text-green-300">
              {language === 'ar' ? 'تم حفظ مؤشر الأداء بنجاح' : 'KPI has been saved successfully'}
            </AlertDescription>
          </Alert>
        )}

        {/* Error Message */}
        {submitError && (
          <Alert className="border-red-500/50 bg-red-500/10">
            <AlertCircle className="h-4 w-4 text-red-400" />
            <AlertTitle className="text-red-400">
              {language === 'ar' ? 'خطأ' : 'Error'}
            </AlertTitle>
            <AlertDescription className="text-red-300">
              {submitError}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-white">{t.name}</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`kpi-input glass-input ${errors.name ? 'border-red-500' : ''}`}
                required
              />
              {errors.name && (
                <p className="text-red-400 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            {/* Name Arabic */}
            <div className="space-y-2">
              <Label htmlFor="name_ar" className="text-white">{t.nameAr}</Label>
              <Input
                id="name_ar"
                value={formData.name_ar}
                onChange={(e) => handleInputChange('name_ar', e.target.value)}
                className={`glass-input ${errors.name_ar ? 'border-red-500' : ''}`}
                dir="rtl"
                required
              />
              {errors.name_ar && (
                <p className="text-red-400 text-sm mt-1">{errors.name_ar}</p>
              )}
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category" className="text-white">{t.category}</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger className={`kpi-select glass-input ${errors.category ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder={t.selectCategory || "Select category"} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {language === 'ar' ? category.name_ar : category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-red-400 text-sm mt-1">{errors.category}</p>
              )}
            </div>

            {/* Measurement Type */}
            <div className="space-y-2">
              <Label htmlFor="measurement_type" className="text-white">{t.measurementType}</Label>
              <Select value={formData.measurement_type} onValueChange={(value) => handleInputChange('measurement_type', value)}>
                <SelectTrigger className="glass-input">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="NUMBER">Number</SelectItem>
                  <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                  <SelectItem value="CURRENCY">Currency</SelectItem>
                  <SelectItem value="SCORE">Score</SelectItem>
                  <SelectItem value="RATIO">Ratio</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Unit */}
            <div className="space-y-2">
              <Label htmlFor="unit" className="text-white">{t.unit}</Label>
              <Input
                id="unit"
                value={formData.unit}
                onChange={(e) => handleInputChange('unit', e.target.value)}
                className="glass-input"
                placeholder="e.g., %, $, points"
              />
            </div>

            {/* Frequency */}
            <div className="space-y-2">
              <Label htmlFor="frequency" className="text-white">{t.frequency}</Label>
              <Select value={formData.frequency} onValueChange={(value) => handleInputChange('frequency', value)}>
                <SelectTrigger className="glass-input">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="DAILY">Daily</SelectItem>
                  <SelectItem value="WEEKLY">Weekly</SelectItem>
                  <SelectItem value="MONTHLY">Monthly</SelectItem>
                  <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                  <SelectItem value="YEARLY">Yearly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Target Value */}
            <div className="space-y-2">
              <Label htmlFor="target_value" className="text-white">{t.targetValue}</Label>
              <Input
                id="target_value"
                type="number"
                value={formData.target_value}
                onChange={(e) => handleInputChange('target_value', parseFloat(e.target.value) || 0)}
                className="glass-input"
              />
            </div>

            {/* Warning Threshold */}
            <div className="space-y-2">
              <Label htmlFor="warning_threshold" className="text-white">{t.warningThreshold}</Label>
              <Input
                id="warning_threshold"
                type="number"
                value={formData.warning_threshold}
                onChange={(e) => handleInputChange('warning_threshold', parseFloat(e.target.value) || 0)}
                className="glass-input"
              />
            </div>

            {/* Critical Threshold */}
            <div className="space-y-2">
              <Label htmlFor="critical_threshold" className="text-white">{t.criticalThreshold}</Label>
              <Input
                id="critical_threshold"
                type="number"
                value={formData.critical_threshold}
                onChange={(e) => handleInputChange('critical_threshold', parseFloat(e.target.value) || 0)}
                className="glass-input"
              />
            </div>

            {/* Data Source */}
            <div className="space-y-2">
              <Label htmlFor="data_source" className="text-white">{t.dataSource}</Label>
              <Input
                id="data_source"
                value={formData.data_source}
                onChange={(e) => handleInputChange('data_source', e.target.value)}
                className="glass-input"
                placeholder="e.g., CRM System, Financial Database"
              />
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-white">{t.description}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="glass-input"
              rows={3}
            />
          </div>

          {/* Description Arabic */}
          <div className="space-y-2">
            <Label htmlFor="description_ar" className="text-white">{t.descriptionAr}</Label>
            <Textarea
              id="description_ar"
              value={formData.description_ar}
              onChange={(e) => handleInputChange('description_ar', e.target.value)}
              className="glass-input"
              rows={3}
              dir="rtl"
            />
          </div>

          {/* Formula */}
          <div className="space-y-2">
            <Label htmlFor="formula" className="text-white">{t.formula}</Label>
            <Textarea
              id="formula"
              value={formData.formula}
              onChange={(e) => handleInputChange('formula', e.target.value)}
              className="glass-input"
              rows={2}
              placeholder="e.g., (Current Value / Target Value) * 100"
            />
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="kpi-button glass-button"
              disabled={loading}
            >
              {t.cancel}
            </Button>
            <Button
              type="submit"
              className="kpi-button glass-button"
              disabled={loading}
            >
              {loading ? t.saving : t.save}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
