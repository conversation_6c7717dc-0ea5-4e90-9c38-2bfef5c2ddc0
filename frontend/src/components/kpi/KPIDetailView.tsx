/**
 * KPI Detail View with Tab Navigation
 * Comprehensive view for displaying KPI details with multiple tabs
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Target, 
  AlertTriangle, 
  Activity,
  Calendar,
  Settings,
  History,
  Bell,
  Edit,
  Plus
} from 'lucide-react'
import { KPI, KPIValue, KPIAlert } from '@/services/kpiService'
import kpiService from '@/services/kpiService'
import { useKPIModals } from '@/hooks/useKPIModals'

interface KPIDetailViewProps {
  kpi: KPI
  language: 'ar' | 'en'
  onClose?: () => void
}

const translations = {
  ar: {
    overview: 'نظرة عامة',
    values: 'القيم',
    trends: 'الاتجاهات',
    alerts: 'التنبيهات',
    settings: 'الإعدادات',
    currentValue: 'القيمة الحالية',
    targetValue: 'القيمة المستهدفة',
    achievement: 'الإنجاز',
    lastUpdated: 'آخر تحديث',
    addValue: 'إضافة قيمة',
    editKPI: 'تعديل المؤشر',
    setTarget: 'تحديد الهدف',
    noValues: 'لا توجد قيم',
    noAlerts: 'لا توجد تنبيهات',
    description: 'الوصف',
    category: 'الفئة',
    frequency: 'التكرار',
    unit: 'الوحدة',
    dataSource: 'مصدر البيانات',
    owner: 'المالك',
    status: 'الحالة',
    createdAt: 'تاريخ الإنشاء'
  },
  en: {
    overview: 'Overview',
    values: 'Values',
    trends: 'Trends',
    alerts: 'Alerts',
    settings: 'Settings',
    currentValue: 'Current Value',
    targetValue: 'Target Value',
    achievement: 'Achievement',
    lastUpdated: 'Last Updated',
    addValue: 'Add Value',
    editKPI: 'Edit KPI',
    setTarget: 'Set Target',
    noValues: 'No values recorded',
    noAlerts: 'No active alerts',
    description: 'Description',
    category: 'Category',
    frequency: 'Frequency',
    unit: 'Unit',
    dataSource: 'Data Source',
    owner: 'Owner',
    status: 'Status',
    createdAt: 'Created At'
  }
}

export default function KPIDetailView({ kpi, language, onClose }: KPIDetailViewProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [values, setValues] = useState<KPIValue[]>([])
  const [alerts, setAlerts] = useState<KPIAlert[]>([])
  const [loading, setLoading] = useState(false)
  
  const kpiModals = useKPIModals()
  const t = translations[language]
  const isRTL = language === 'ar'

  // Load KPI values and alerts
  useEffect(() => {
    const loadKPIData = async () => {
      setLoading(true)
      try {
        // Load values and alerts for this KPI
        const kpiValues = await kpiService.getKPIValues(kpi.id, {
          limit: 20
        })
        setValues(kpiValues)

        // Load alerts if available
        try {
          const kpiAlerts = await kpiService.getAlerts({
            limit: 10
          })
          setAlerts(kpiAlerts)
        } catch (alertError) {
          console.error('Error loading KPI alerts:', alertError)
          setAlerts([])
        }
      } catch (error) {
        console.error('Error loading KPI data:', error)
        setValues([])
        setAlerts([])
      } finally {
        setLoading(false)
      }
    }

    loadKPIData()
  }, [kpi.id])

  const handleAddValue = () => {
    kpiModals.openAddValueModal(kpi, async (valueData) => {
      // Refresh values after adding
      console.log('Value added:', valueData)
    })
  }

  const handleEditKPI = () => {
    kpiModals.openEditKPIModal(kpi, [], async (kpiData) => {
      // Refresh KPI after editing
      console.log('KPI updated:', kpiData)
    })
  }

  const handleSetTarget = () => {
    kpiModals.openTargetModal(kpi, async (targetData) => {
      // Refresh KPI after setting target
      console.log('Target set:', targetData)
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'INACTIVE':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
      case 'DRAFT':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default:
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    }
  }

  const getTrendIcon = () => {
    if (!kpi.trend) return <Activity className="h-4 w-4" />
    
    switch (kpi.trend.direction) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-400" />
      default:
        return <Activity className="h-4 w-4 text-gray-400" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === 'ar' ? 'ar-SA' : 'en-US',
      {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }
    )
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-white text-2xl mb-2">
                {language === 'ar' ? kpi.name_ar || kpi.name : kpi.name}
              </CardTitle>
              <p className="text-white/70 mb-4">
                {language === 'ar' ? kpi.description_ar || kpi.description : kpi.description}
              </p>
              <div className="flex items-center gap-4">
                <Badge className={getStatusColor(kpi.status)}>
                  {kpi.status}
                </Badge>
                <Badge variant="outline" className="text-white/70">
                  {language === 'ar' ? kpi.category_name_ar || kpi.category_name : kpi.category_name}
                </Badge>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={handleAddValue} size="sm" className="glass-button">
                <Plus className="h-4 w-4 mr-2" />
                {t.addValue}
              </Button>
              <Button onClick={handleEditKPI} size="sm" variant="outline" className="glass-button">
                <Edit className="h-4 w-4 mr-2" />
                {t.editKPI}
              </Button>
              <Button onClick={handleSetTarget} size="sm" variant="outline" className="glass-button">
                <Target className="h-4 w-4 mr-2" />
                {t.setTarget}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Tab Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="glass-card border-white/20 p-1 w-full">
          <TabsTrigger value="overview" className="glass-tab flex-1">
            <BarChart3 className="h-4 w-4 mr-2" />
            {t.overview}
          </TabsTrigger>
          <TabsTrigger value="values" className="glass-tab flex-1">
            <Activity className="h-4 w-4 mr-2" />
            {t.values}
          </TabsTrigger>
          <TabsTrigger value="trends" className="glass-tab flex-1">
            <TrendingUp className="h-4 w-4 mr-2" />
            {t.trends}
          </TabsTrigger>
          <TabsTrigger value="alerts" className="glass-tab flex-1">
            <Bell className="h-4 w-4 mr-2" />
            {t.alerts}
          </TabsTrigger>
          <TabsTrigger value="settings" className="glass-tab flex-1">
            <Settings className="h-4 w-4 mr-2" />
            {t.settings}
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Current Value */}
            <Card className="glass-card border-white/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/70 text-sm">{t.currentValue}</span>
                  {getTrendIcon()}
                </div>
                <div className="text-2xl font-bold text-white">
                  {kpi.current_value?.value || 0} {kpi.unit}
                </div>
                <div className="text-white/50 text-xs mt-1">
                  {t.lastUpdated}: {kpi.current_value?.recorded_at ? formatDate(kpi.current_value.recorded_at) : 'N/A'}
                </div>
              </CardContent>
            </Card>

            {/* Target Value */}
            <Card className="glass-card border-white/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/70 text-sm">{t.targetValue}</span>
                  <Target className="h-4 w-4 text-blue-400" />
                </div>
                <div className="text-2xl font-bold text-white">
                  {kpi.target_value || 0} {kpi.unit}
                </div>
              </CardContent>
            </Card>

            {/* Achievement */}
            <Card className="glass-card border-white/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/70 text-sm">{t.achievement}</span>
                  <BarChart3 className="h-4 w-4 text-green-400" />
                </div>
                <div className="text-2xl font-bold text-white">
                  {kpi.target_achievement || 0}%
                </div>
              </CardContent>
            </Card>
          </div>

          {/* KPI Details */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">KPI Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-white/70 text-sm">{t.category}</label>
                  <p className="text-white">{language === 'ar' ? kpi.category_name_ar || kpi.category_name : kpi.category_name}</p>
                </div>
                <div>
                  <label className="text-white/70 text-sm">{t.frequency}</label>
                  <p className="text-white">{kpi.frequency}</p>
                </div>
                <div>
                  <label className="text-white/70 text-sm">{t.unit}</label>
                  <p className="text-white">{kpi.unit || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-white/70 text-sm">{t.dataSource}</label>
                  <p className="text-white">{kpi.data_source || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-white/70 text-sm">{t.owner}</label>
                  <p className="text-white">{kpi.owner_name || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-white/70 text-sm">{t.createdAt}</label>
                  <p className="text-white">{formatDate(kpi.created_at)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Values Tab */}
        <TabsContent value="values">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                {t.values}
                <Button onClick={handleAddValue} size="sm" className="glass-button">
                  <Plus className="h-4 w-4 mr-2" />
                  {t.addValue}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {values.length === 0 ? (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-white/30 mx-auto mb-4" />
                  <p className="text-white/70">{t.noValues}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Values list would go here */}
                  <p className="text-white/70">Values list implementation</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">{t.trends}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-white/30 mx-auto mb-4" />
                <p className="text-white/70">Trend analysis chart would go here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">{t.alerts}</CardTitle>
            </CardHeader>
            <CardContent>
              {alerts.length === 0 ? (
                <div className="text-center py-8">
                  <Bell className="h-12 w-12 text-white/30 mx-auto mb-4" />
                  <p className="text-white/70">{t.noAlerts}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Alerts list would go here */}
                  <p className="text-white/70">Alerts list implementation</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">{t.settings}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button onClick={handleEditKPI} className="glass-button w-full">
                  <Edit className="h-4 w-4 mr-2" />
                  {t.editKPI}
                </Button>
                <Button onClick={handleSetTarget} className="glass-button w-full">
                  <Target className="h-4 w-4 mr-2" />
                  {t.setTarget}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
