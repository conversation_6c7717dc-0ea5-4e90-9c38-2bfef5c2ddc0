/**
 * Sidebar Navigation Component
 * Handles the main navigation sidebar with role-based menu items
 */

import React, { memo, useMemo } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { getNavigationForRole, NavigationItem } from './navigationConfig'
import { navigationTranslations } from './translations'

interface SidebarProps {
  isOpen: boolean
  userRole: string
  language: 'ar' | 'en'
  expandedMenus: string[]
  onToggleMenu: (menuId: string) => void
  onClose?: () => void
}

const Sidebar: React.FC<SidebarProps> = memo(({
  isOpen,
  userRole,
  language,
  expandedMenus,
  onToggleMenu,
  onClose
}) => {
  const location = useLocation()
  const t = useMemo(() => navigationTranslations[language], [language])
  const navigation = useMemo(() => getNavigationForRole(userRole, t), [userRole, t])

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Sidebar render - isOpen:', isOpen, 'language:', language)
  }

  const isActiveRoute = useMemo(() => (href: string) => {
    if (href === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(href)
  }, [location.pathname])

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const Icon = item.icon
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = item.id ? expandedMenus.includes(item.id) : false
    const isActive = item.href ? isActiveRoute(item.href) : false

    if (hasChildren) {
      return (
        <div key={item.id || item.name} className="space-y-1">
          <button
            onClick={() => item.id && onToggleMenu(item.id)}
            className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
              isActive
                ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
                : 'text-white/80 hover:text-white hover:bg-white/10'
            }`}
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Icon className="h-5 w-5 flex-shrink-0" />
              <span className="truncate">{item.name}</span>
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 flex-shrink-0" />
            ) : (
              <ChevronRight className="h-4 w-4 flex-shrink-0" />
            )}
          </button>

          {isExpanded && (
            <div className="ml-6 rtl:mr-6 rtl:ml-0 space-y-1">
              {item.children?.map(child => renderNavigationItem(child, level + 1))}
            </div>
          )}
        </div>
      )
    }

    return (
      <Link
        key={item.href || item.name}
        to={item.href || '#'}
        onClick={onClose}
        className={`flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
          isActive
            ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
            : 'text-white/80 hover:text-white hover:bg-white/10'
        }`}
      >
        <Icon className="h-5 w-5 flex-shrink-0" />
        <span className="truncate">{item.name}</span>
      </Link>
    )
  }

  return (
    <>
      {/* Mobile Overlay - Fixed z-index and pointer events */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
          style={{ touchAction: 'none', pointerEvents: 'auto' }}
          onClick={() => {
            console.log('Overlay clicked, closing sidebar')
            onClose?.()
          }}
        />
      )}

      {/* Sidebar - Fixed z-index and pointer events */}
      <div
        className={`
          sidebar-container
          bg-gradient-to-b from-slate-900/95 to-purple-900/95 backdrop-blur-xl
          border-white/10
          ${language === 'ar' ? 'right-0 border-l' : 'left-0 border-r'}
          ${isOpen
            ? 'sidebar-open'
            : language === 'ar'
              ? 'sidebar-closed-rtl'
              : 'sidebar-closed-ltr'
          }
        `}
        style={{
          pointerEvents: isOpen ? 'auto' : 'none',
          visibility: isOpen ? 'visible' : 'hidden'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-center h-16 px-6 border-b border-white/10">
          <div className={`flex items-center gap-3 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">ن</span>
            </div>
            <span className="text-xl font-bold text-white">نمو</span>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto max-h-full">
          {navigation.map(item => renderNavigationItem(item))}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-white/10">
          <div className="text-center text-xs text-white/50">
            نمو EMS v1.0.0
          </div>
        </div>
      </div>
    </>
  )
})

Sidebar.displayName = 'Sidebar'

export default Sidebar
