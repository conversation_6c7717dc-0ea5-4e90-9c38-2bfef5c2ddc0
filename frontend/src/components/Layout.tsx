import React, { <PERSON>actN<PERSON>, useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import type { RootState } from '../store'
import { logoutUser } from '../store/slices/authSlice'
import { Sidebar, Header } from './navigation'
import PureSuperAdminNavigation from './navigation/PureSuperAdminNavigation'
import { PermissionGate, usePermissions } from './RoleBasedRoute'

interface LayoutProps {
  children: ReactNode
  language: 'ar' | 'en'
  setLanguage: (lang: 'ar' | 'en') => void
}

// Hook to detect device type
const useDeviceType = () => {
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')

  useEffect(() => {
    const checkDeviceType = () => {
      const width = window.innerWidth
      if (width < 768) {
        setDeviceType('mobile')
      } else if (width < 1024) {
        setDeviceType('tablet')
      } else {
        setDeviceType('desktop')
      }
    }

    checkDeviceType()
    window.addEventListener('resize', checkDeviceType)
    return () => window.removeEventListener('resize', checkDeviceType)
  }, [])

  return deviceType
}

export default function Layout({ children, language, setLanguage }: LayoutProps) {
  const dispatch = useDispatch()
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth)
  const { unreadCount } = useSelector((state: RootState) => state.notifications)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState<string[]>([])
  const deviceType = useDeviceType()

  // ALWAYS call hooks - never conditionally
  const permissions = usePermissions()

  // Auto-close sidebar on mobile by default
  useEffect(() => {
    if (deviceType === 'mobile') {
      setSidebarOpen(false)
    } else if (deviceType === 'desktop') {
      setSidebarOpen(true)
    }
  }, [deviceType])

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (deviceType === 'mobile' && sidebarOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [sidebarOpen, deviceType])

  // Keyboard navigation support
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Close sidebar on Escape key
      if (event.key === 'Escape' && sidebarOpen && deviceType === 'mobile') {
        closeSidebar()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [sidebarOpen, deviceType])

  const handleLogout = () => {
    dispatch(logoutUser() as any)
  }

  const toggleMenu = (menuId: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuId)
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    )
  }

  const toggleSidebar = () => {
    console.log('Toggling sidebar from:', sidebarOpen, 'to:', !sidebarOpen)
    setSidebarOpen(prev => !prev)
  }

  const closeSidebar = () => {
    console.log('Closing sidebar')
    setSidebarOpen(false)
  }

  // Get user role for navigation
  const userRole = user?.role?.id || 'employee'

  // Layout should only render when user is authenticated and available
  // If not, let App component handle the authentication flow
  if (!isAuthenticated) {
    console.log('Layout: Not authenticated, redirecting to authentication flow')
    return null
  }

  if (!user) {
    console.log('Layout: User data not loaded yet, showing loading...')
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-xl">جاري تحميل بيانات المستخدم...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      {/* Layout Container */}
      <div className="relative z-10 flex h-screen">
        {/* Mobile Backdrop - Only visible on mobile when sidebar is open */}
        {deviceType === 'mobile' && sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            onClick={() => {
              console.log('Mobile backdrop clicked, forcing sidebar close');
              setSidebarOpen(false);
            }}
            style={{ touchAction: 'none' }}
          />
        )}

        {/* Sidebar - Different navigation for SUPERADMIN */}
        {userRole === 'super_admin' ? (
          <div className="w-80 h-full">
            <PureSuperAdminNavigation
              language={language}
              onLogout={handleLogout}
            />
          </div>
        ) : (
          <Sidebar
            isOpen={sidebarOpen}
            userRole={userRole}
            language={language}
            expandedMenus={expandedMenus}
            onToggleMenu={toggleMenu}
            onClose={closeSidebar}
          />
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col transition-all duration-300">
          {/* Header */}
          <Header
            sidebarOpen={sidebarOpen}
            onToggleSidebar={toggleSidebar}
            language={language}
            onLanguageChange={setLanguage}
            user={user}
            unreadCount={unreadCount}
          />

          {/* Page Content */}
          <main className="flex-1 overflow-auto w-full">
            <div className="p-4 lg:p-6 w-full max-w-none min-w-0">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
