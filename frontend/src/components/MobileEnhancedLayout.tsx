import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { motion, AnimatePresence } from 'framer-motion'
import Header from './navigation/Header'
import Sidebar from './navigation/Sidebar'
import type { RootState } from '../store'
import {
  Smartphone,
  Tablet,
  Monitor,
  Wifi,
  WifiOff,
  Battery,
  Signal
} from 'lucide-react'

interface MobileEnhancedLayoutProps {
  children: React.ReactNode
  language: 'ar' | 'en'
  setLanguage: (lang: 'ar' | 'en') => void
}

export default function MobileEnhancedLayout({ 
  children, 
  language, 
  setLanguage 
}: MobileEnhancedLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop')
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [touchSupport, setTouchSupport] = useState(false)
  
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth)
  const { unreadCount } = useSelector((state: RootState) => state.notifications)

  // Detect device type and capabilities
  useEffect(() => {
    const detectDevice = () => {
      const width = window.innerWidth
      if (width < 768) {
        setDeviceType('mobile')
      } else if (width < 1024) {
        setDeviceType('tablet')
      } else {
        setDeviceType('desktop')
      }
    }

    const detectTouch = () => {
      setTouchSupport('ontouchstart' in window || navigator.maxTouchPoints > 0)
    }

    detectDevice()
    detectTouch()

    window.addEventListener('resize', detectDevice)
    return () => window.removeEventListener('resize', detectDevice)
  }, [])

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Auto-close sidebar on mobile when route changes
  useEffect(() => {
    if (deviceType === 'mobile') {
      setSidebarOpen(false)
    }
  }, [children, deviceType])

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (deviceType === 'mobile' && sidebarOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [sidebarOpen, deviceType])

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: language === 'ar' ? '100%' : '-100%',
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  const overlayVariants = {
    open: {
      opacity: 1,
      transition: { duration: 0.2 }
    },
    closed: {
      opacity: 0,
      transition: { duration: 0.2 }
    }
  }

  const mainContentVariants = {
    mobile: {
      marginLeft: 0,
      marginRight: 0
    },
    desktop: {
      marginLeft: language === 'ar' ? 0 : sidebarOpen ? '16rem' : 0,
      marginRight: language === 'ar' ? sidebarOpen ? '16rem' : 0 : 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  if (!isAuthenticated) {
    return <>{children}</>
  }

  return (
    <div className={`min-h-screen gradient-bg ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Network Status Indicator */}
      {!isOnline && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-red-500 text-white text-center py-2 text-sm">
          <div className="flex items-center justify-center gap-2">
            <WifiOff className="h-4 w-4" />
            <span>{language === 'ar' ? 'لا يوجد اتصال بالإنترنت' : 'No internet connection'}</span>
          </div>
        </div>
      )}

      {/* Device Type Indicator (Development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-50 glass-card p-2 rounded-lg">
          <div className="flex items-center gap-2 text-white text-xs">
            {deviceType === 'mobile' && <Smartphone className="h-3 w-3" />}
            {deviceType === 'tablet' && <Tablet className="h-3 w-3" />}
            {deviceType === 'desktop' && <Monitor className="h-3 w-3" />}
            <span>{deviceType}</span>
            {touchSupport && <span>• Touch</span>}
            {isOnline ? <Wifi className="h-3 w-3 text-green-400" /> : <WifiOff className="h-3 w-3 text-red-400" />}
          </div>
        </div>
      )}

      {/* Header */}
      <Header
        sidebarOpen={sidebarOpen}
        onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
        language={language}
        onLanguageChange={setLanguage}
        user={user}
        unreadCount={unreadCount}
      />

      {/* Mobile Sidebar Overlay */}
      <AnimatePresence>
        {sidebarOpen && deviceType === 'mobile' && (
          <motion.div
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
            variants={overlayVariants}
            initial="closed"
            animate="open"
            exit="closed"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <AnimatePresence>
        {(sidebarOpen || deviceType === 'desktop') && (
          <motion.div
            className={`
              fixed top-16 bottom-0 z-50 w-64
              ${deviceType === 'mobile' ? 'shadow-2xl' : ''}
              ${language === 'ar' ? 'right-0' : 'left-0'}
            `}
            variants={deviceType === 'mobile' ? sidebarVariants : {}}
            initial={deviceType === 'mobile' ? "closed" : false}
            animate={deviceType === 'mobile' ? "open" : {}}
            exit={deviceType === 'mobile' ? "closed" : {}}
          >
            <Sidebar
              language={language}
              onNavigate={() => {
                if (deviceType === 'mobile') {
                  setSidebarOpen(false)
                }
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <motion.main
        className={`
          pt-16 min-h-screen
          ${deviceType === 'desktop' && sidebarOpen ? (language === 'ar' ? 'mr-64' : 'ml-64') : ''}
          transition-all duration-300 ease-in-out
        `}
        variants={deviceType === 'mobile' ? mainContentVariants.mobile : mainContentVariants.desktop}
        animate={deviceType === 'mobile' ? 'mobile' : 'desktop'}
      >
        <div className={`
          p-4 lg:p-6
          ${deviceType === 'mobile' ? 'pb-20' : ''}
        `}>
          {children}
        </div>
      </motion.main>

      {/* Mobile Bottom Navigation (Optional) */}
      {deviceType === 'mobile' && (
        <div className="fixed bottom-0 left-0 right-0 z-30 glass-card border-t border-white/20">
          <div className="flex items-center justify-around py-2">
            {/* Quick access buttons for mobile */}
            <button className="flex flex-col items-center p-2 text-white/70 hover:text-white">
              <div className="w-6 h-6 mb-1">🏠</div>
              <span className="text-xs">{language === 'ar' ? 'الرئيسية' : 'Home'}</span>
            </button>
            <button className="flex flex-col items-center p-2 text-white/70 hover:text-white">
              <div className="w-6 h-6 mb-1">📊</div>
              <span className="text-xs">{language === 'ar' ? 'التقارير' : 'Reports'}</span>
            </button>
            <button className="flex flex-col items-center p-2 text-white/70 hover:text-white">
              <div className="w-6 h-6 mb-1">💬</div>
              <span className="text-xs">{language === 'ar' ? 'الرسائل' : 'Messages'}</span>
            </button>
            <button className="flex flex-col items-center p-2 text-white/70 hover:text-white">
              <div className="w-6 h-6 mb-1">⚙️</div>
              <span className="text-xs">{language === 'ar' ? 'الإعدادات' : 'Settings'}</span>
            </button>
          </div>
        </div>
      )}

      {/* Swipe Gesture Handlers for Mobile */}
      {deviceType === 'mobile' && touchSupport && (
        <div
          className={`
            fixed top-16 bottom-0 w-4 z-30
            ${language === 'ar' ? 'right-0' : 'left-0'}
          `}
          onTouchStart={(e) => {
            const touch = e.touches[0]
            const startX = touch.clientX
            
            const handleTouchMove = (e: TouchEvent) => {
              const touch = e.touches[0]
              const currentX = touch.clientX
              const diff = currentX - startX
              
              if (Math.abs(diff) > 50) {
                if (language === 'ar' ? diff < 0 : diff > 0) {
                  setSidebarOpen(true)
                }
                document.removeEventListener('touchmove', handleTouchMove)
              }
            }
            
            document.addEventListener('touchmove', handleTouchMove)
            setTimeout(() => {
              document.removeEventListener('touchmove', handleTouchMove)
            }, 300)
          }}
        />
      )}
    </div>
  )
}
