import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'

interface UserProfile {
  personalInfo: {
    firstName: string
    lastName: string
    firstNameAr: string
    lastNameAr: string
    email: string
    phone: string
    dateOfBirth: string
    nationality: string
    nationalId: string
    address: string
    emergencyContact: {
      name: string
      phone: string
      relationship: string
    }
  }
  workInfo: {
    employeeId: string
    department: string
    position: string
    positionAr: string
    manager: string
    startDate: string
    workLocation: string
    workSchedule: string
    salary: number
    benefits: string[]
  }
  documents: {
    id: string
    name: string
    type: string
    uploadDate: string
    url: string
  }[]
}

interface UserStats {
  attendance: {
    present: number
    absent: number
    late: number
    overtime: number
  }
  leave: {
    available: number
    used: number
    pending: number
  }
  performance: {
    score: number
    goals: number
    achievements: number
  }
  tasks: {
    completed: number
    pending: number
    overdue: number
  }
}

interface UserState {
  profile: UserProfile | null
  stats: UserStats | null
  recentActivities: any[]
  quickActions: any[]
  isLoading: boolean
  error: string | null
}

const initialState: UserState = {
  profile: null,
  stats: null,
  recentActivities: [],
  quickActions: [],
  isLoading: false,
  error: null,
}

// Mock data removed - User profiles should be fetched from backend API

export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (userId: string, { rejectWithValue }) => {
    try {
      // TODO: Replace with actual API call to backend
      // const response = await apiClient.get(`/users/${userId}/profile/`)
      // return response.data
      throw new Error('API not implemented - fetchUserProfile')
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const fetchUserStats = createAsyncThunk(
  'user/fetchStats',
  async (userId: string, { rejectWithValue }) => {
    try {
      // TODO: Replace with actual API call to backend
      // const response = await apiClient.get(`/users/${userId}/stats/`)
      // return response.data
      throw new Error('API not implemented - fetchUserStats')
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (profileData: Partial<UserProfile>, { rejectWithValue }) => {
    try {
      // TODO: Replace with actual API call to backend
      // const response = await apiClient.patch('/users/profile/', profileData)
      // return response.data
      throw new Error('API not implemented - updateUserProfile')
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearUserError: (state) => {
      state.error = null
    },
    addRecentActivity: (state, action) => {
      state.recentActivities.unshift(action.payload)
      if (state.recentActivities.length > 10) {
        state.recentActivities = state.recentActivities.slice(0, 10)
      }
    },
    updateQuickActions: (state, action) => {
      state.quickActions = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch profile
      .addCase(fetchUserProfile.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.isLoading = false
        state.profile = action.payload as unknown as UserProfile
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
      // Fetch stats
      .addCase(fetchUserStats.fulfilled, (state, action) => {
        state.stats = action.payload as unknown as UserStats
      })
      // Update profile
      .addCase(updateUserProfile.pending, (state) => {
        state.isLoading = true
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isLoading = false
        if (state.profile) {
          state.profile = { ...state.profile, ...action.payload }
        }
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
  },
})

export const { clearUserError, addRecentActivity, updateQuickActions } = userSlice.actions
export default userSlice.reducer
