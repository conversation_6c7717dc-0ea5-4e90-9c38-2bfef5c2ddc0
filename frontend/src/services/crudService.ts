/**
 * Generic CRUD Service
 * Provides standardized Create, Read, Update, Delete operations for all entities
 */

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'

export interface CrudResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
}

export interface CrudFilters {
  search?: string
  category?: string
  status?: string
  department?: string
  employee?: string
  startDate?: string
  endDate?: string
  [key: string]: any
}

export interface CrudOptions {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: CrudFilters
}

class CrudService {
  private baseUrl: string

  constructor(endpoint: string) {
    this.baseUrl = `${API_BASE_URL}/${endpoint}`
  }

  // Generic GET request
  private async request<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('access_token') || localStorage.getItem('token')

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        // Handle 401 Unauthorized - token might be expired
        if (response.status === 401) {
          console.warn('401 Unauthorized - token might be expired. Redirecting to login...')
          // Clear invalid tokens
          localStorage.removeItem('access_token')
          localStorage.removeItem('token')
          localStorage.removeItem('refresh_token')
          // Redirect to login page
          window.location.href = '/login'
          throw new Error('Authentication required. Please log in again.')
        }

        // Try to get error details from response
        let errorMessage = `HTTP error! status: ${response.status}`
        try {
          const errorData = await response.json()
          console.error('Server error response:', errorData)
          if (errorData.detail) {
            errorMessage = errorData.detail
          } else if (errorData.message) {
            errorMessage = errorData.message
          } else if (typeof errorData === 'object') {
            errorMessage = JSON.stringify(errorData)
          }
        } catch (e) {
          // If response is not JSON, try to get text
          try {
            const errorText = await response.text()
            console.error('Server error text:', errorText)
            if (errorText) {
              errorMessage = errorText
            }
          } catch (e2) {
            // Keep original error message
          }
        }
        throw new Error(errorMessage)
      }

      return await response.json()
    } catch (error) {
      console.error(`API request failed: ${url}`, error)
      throw error
    }
  }

  // Create (POST)
  async create<T>(data: Partial<T>): Promise<T> {
    console.log('Creating item with data:', data)
    console.log('Endpoint:', `${this.baseUrl}/`)
    return this.request<T>(`${this.baseUrl}/`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // Read (GET) - List with pagination and filters
  async getAll<T>(options: CrudOptions = {}): Promise<CrudResponse<T>> {
    console.log('🔍 CrudService.getAll - Options received:', options)

    const params = new URLSearchParams()

    if (options.page) params.append('page', options.page.toString())
    if (options.pageSize) params.append('page_size', options.pageSize.toString())
    if (options.sortBy) params.append('ordering', options.sortOrder === 'desc' ? `-${options.sortBy}` : options.sortBy)

    // Add filters
    if (options.filters) {
      console.log('🔍 CrudService - Processing filters:', options.filters)
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          console.log(`🔍 CrudService - Adding filter: ${key} = ${value}`)
          params.append(key, value.toString())
        }
      })
    }

    const url = `${this.baseUrl}/${params.toString() ? `?${params.toString()}` : ''}`
    console.log('🔍 CrudService - Final URL:', url)

    const response = await this.request<any>(url)
    console.log('📡 CrudService - Backend response:', response)

    // Transform Django REST Framework response to our CrudResponse format
    if (response.results && Array.isArray(response.results)) {
      return {
        data: response.results,
        total: response.count || response.results.length,
        page: options.page || 1,
        pageSize: options.pageSize || response.results.length
      }
    }

    // Fallback for non-paginated responses
    if (Array.isArray(response)) {
      return {
        data: response,
        total: response.length,
        page: 1,
        pageSize: response.length
      }
    }

    // If response already matches our format
    return response
  }

  // Read (GET) - Single item by ID
  async getById<T>(id: string | number): Promise<T> {
    return this.request<T>(`${this.baseUrl}/${id}/`)
  }

  // Update (PUT/PATCH)
  async update<T>(id: string | number, data: Partial<T>, partial = true): Promise<T> {
    return this.request<T>(`${this.baseUrl}/${id}/`, {
      method: partial ? 'PATCH' : 'PUT',
      body: JSON.stringify(data),
    })
  }

  // Delete (DELETE)
  async delete(id: string | number): Promise<void> {
    return this.request<void>(`${this.baseUrl}/${id}/`, {
      method: 'DELETE',
    })
  }

  // Bulk operations
  async bulkCreate<T>(items: Partial<T>[]): Promise<T[]> {
    return this.request<T[]>(`${this.baseUrl}/bulk/`, {
      method: 'POST',
      body: JSON.stringify({ items }),
    })
  }

  async bulkUpdate<T>(updates: { id: string | number; data: Partial<T> }[]): Promise<T[]> {
    return this.request<T[]>(`${this.baseUrl}/bulk/`, {
      method: 'PATCH',
      body: JSON.stringify({ updates }),
    })
  }

  async bulkDelete(ids: (string | number)[]): Promise<void> {
    return this.request<void>(`${this.baseUrl}/bulk/`, {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    })
  }

  // Search
  async search<T>(query: string, options: CrudOptions = {}): Promise<CrudResponse<T>> {
    return this.getAll<T>({
      ...options,
      filters: {
        ...options.filters,
        search: query,
      },
    })
  }

  // Export
  async export(format: 'csv' | 'excel' | 'pdf' = 'csv', filters?: CrudFilters): Promise<Blob> {
    const params = new URLSearchParams()
    params.append('format', format)

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString())
        }
      })
    }

    const token = localStorage.getItem('access_token') || localStorage.getItem('token')

    // Extract the resource name from the baseUrl (e.g., 'employees' from '/api/employees')
    const resourceName = this.baseUrl.split('/').pop()

    // Check if backend export endpoint exists for this resource
    const backendExportEndpoints = [
      'employees', 'departments', 'attendance', 'projects', 'tasks',
      'quotations', 'workflows', 'quality-records', 'job-postings',
      'sales-customers', 'sales-orders', 'certifications', 'training-programs',
      'vendors', 'kpis'
    ]

    if (backendExportEndpoints.includes(resourceName || '')) {
      // Use backend export endpoint
      const url = `/api/export/${resourceName}/?${params.toString()}`

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Export failed: ${response.status} - ${errorText}`)
      }

      return response.blob()
    } else {
      // For endpoints without backend export support, return a message
      const message = `Export not available for ${resourceName}. Backend endpoint not implemented.`
      console.warn(message)
      throw new Error(message)
    }
  }

  // Import
  async import(file: File): Promise<{ success: number; errors: any[] }> {
    const formData = new FormData()
    formData.append('file', file)

    const token = localStorage.getItem('access_token') || localStorage.getItem('token')
    const response = await fetch(`${this.baseUrl}/import/`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      throw new Error(`Import failed: ${response.status}`)
    }

    return response.json()
  }
}

// Factory function to create CRUD services for different entities
export const createCrudService = (endpoint: string) => new CrudService(endpoint)

// Pre-configured services for common entities
// Import employeeAPI directly
import { employeeAPI } from './employeeAPI'

// Special employee service with data transformation
export const employeeService = {
  ...createCrudService('employees'),
  async getAll(params?: any) {
    return employeeAPI.getAll(params)
  },
  async getById(id: number) {
    return employeeAPI.getById(id)
  },
  async create(data: any) {
    return employeeAPI.create(data)
  },
  async update(id: number, data: any) {
    return employeeAPI.update(id, data)
  },
  async delete(id: number) {
    return employeeAPI.delete(id)
  }
}
export const departmentService = createCrudService('departments')
// Removed duplicate - already declared below
export const projectService = createCrudService('projects')
export const taskService = createCrudService('tasks')
export const leaveService = createCrudService('leave-requests')
export const leaveManagementService = createCrudService('leave-requests') // Alternative naming
export const attendanceService = createCrudService('attendance')
export const expenseService = createCrudService('expenses')
export const assetService = createCrudService('assets')
export const supplierService = createCrudService('suppliers')
export const customerService = createCrudService('customers')
export const productService = createCrudService('products')
export const orderService = createCrudService('orders')
export const salesOrderService = createCrudService('sales-orders')
export const quotationService = createCrudService('quotations')
export const leadService = createCrudService('leads')
export const meetingService = createCrudService('meetings')
export const documentService = createCrudService('documents')
export const messageService = createCrudService('messages')
export const announcementService = createCrudService('announcements')
export const reportService = createCrudService('reports')
export const budgetService = createCrudService('budgets')
export const inventoryService = createCrudService('inventory')
export const vendorService = createCrudService('vendors')
export const performanceService = createCrudService('performance-reviews')
export const payrollService = createCrudService('payroll')
export const trainingService = createCrudService('training-programs')
export const complianceService = createCrudService('compliance-audits')
export const workflowService = createCrudService('workflows')

// Additional services for missing entities
export const purchaseOrderService = createCrudService('purchase-orders')
export const invoiceService = createCrudService('invoices')
export const ticketService = createCrudService('tickets')
export const calendarEventService = createCrudService('calendar-events')
export const trainingProgramService = createCrudService('training-programs')
export const certificationService = createCrudService('certifications')
export const complianceAuditService = createCrudService('compliance-audits')
export const riskManagementService = createCrudService('risk-management')
export const knowledgeBaseService = createCrudService('knowledge-base')
export const kpiService = createCrudService('kpis')
export const jobPostingService = createCrudService('job-postings')
export const jobPostingsService = createCrudService('job-postings') // Alternative naming
// HR Employees service with data transformation (same as employeeService)
export const hrEmployeesService = {
  ...createCrudService('employees'), // Use 'employees' endpoint, not 'hr-employees'
  async getAll(params?: any) {
    return employeeAPI.getAll(params)
  },
  async getById(id: number) {
    return employeeAPI.getById(id)
  },
  async create(data: any) {
    return employeeAPI.create(data)
  },
  async update(id: number, data: any) {
    return employeeAPI.update(id, data)
  },
  async delete(id: number) {
    return employeeAPI.delete(id)
  }
}
export const hrDepartmentService = createCrudService('hr-departments')
export const hrReportService = createCrudService('hr-reports')
export const candidateService = createCrudService('candidates')
export const interviewService = createCrudService('interviews')
export const onboardingService = createCrudService('onboarding')
export const offboardingService = createCrudService('offboarding')
export const timeTrackingService = createCrudService('time-tracking')
export const projectReportService = createCrudService('project-reports')
export const salesReportService = createCrudService('sales-reports')
export const analyticsService = createCrudService('analytics')
export const businessIntelligenceService = createCrudService('business-intelligence')
export const advancedAnalyticsService = createCrudService('advanced-analytics')
export const qualityManagementService = createCrudService('quality-management')
export const qualityRecordService = createCrudService('quality-records') // Alternative naming
export const customerFeedbackService = createCrudService('customer-feedback')
export const auditService = createCrudService('audits')
export const contractService = createCrudService('contracts')
export const policyService = createCrudService('policies')
export const procedureService = createCrudService('procedures')
export const notificationService = createCrudService('notifications')
export const settingsService = createCrudService('settings')
export const userService = createCrudService('users')
export const roleService = createCrudService('roles')
export const permissionService = createCrudService('permissions')
export const integrationService = createCrudService('integrations')
export const backupService = createCrudService('backups')
export const logService = createCrudService('logs')
export const systemHealthService = createCrudService('system-health')

// Personal Services
export const personalProfileService = createCrudService('personal-profiles')
export const personalMessageService = createCrudService('personal-messages')
export const personalCalendarService = createCrudService('personal-calendar')

// Finance Services
export const financeBudgetService = createCrudService('finance-budgets')
export const financeCustomerService = createCrudService('finance-customers')
export const financeReportService = createCrudService('finance-reports')

// Sales Services
export const salesCustomerService = createCrudService('sales-customers')

// Department Services
export const departmentProjectService = createCrudService('department-projects')
export const departmentCustomerService = createCrudService('department-customers')

// Employee Services
export const employeeTaskService = createCrudService('employee-tasks')
export const employeeLeaveService = createCrudService('employee-leave')

// Admin Services
export const userManagementService = createCrudService('users')

export default CrudService
