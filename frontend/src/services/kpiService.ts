import { apiClient } from './api'

export interface KPICategory {
  id: string
  name: string
  name_ar: string
  description: string
  description_ar: string
  icon: string
  color: string
  is_active: boolean
  sort_order: number
  kpi_count: number
  created_at: string
  updated_at: string
}

export interface KPI {
  id: string
  name: string
  name_ar: string
  description: string
  description_ar: string
  category: string
  category_name: string
  category_name_ar: string
  measurement_type: 'NUMBER' | 'PERCENTAGE' | 'CURRENCY' | 'RATIO' | 'SCORE' | 'TIME'
  unit: string
  unit_ar: string
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
  trend_direction: 'UP' | 'DOWN' | 'TARGET'
  formula: string
  data_source: string
  calculation_method: string
  target_value: number | null
  warning_threshold: number | null
  critical_threshold: number | null
  visible_to_roles: string[]
  owner: string | null
  owner_name: string
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'ARCHIVED'
  is_automated: boolean
  automation_config: Record<string, any>
  created_by: string
  created_by_name: string
  created_at: string
  updated_at: string
  current_value: {
    value: number
    period_start: string
    period_end: string
    recorded_at: string
  } | null
  target_achievement: number | null
  trend: {
    change_percentage: number
    direction: 'up' | 'down' | 'stable'
  } | null
}

export interface KPIValue {
  id: string
  kpi: string
  kpi_name: string
  kpi_unit: string
  value: number
  period_start: string
  period_end: string
  department: string | null
  department_name: string
  project: string | null
  project_name: string
  employee: string | null
  employee_name: string
  data_quality_score: number
  confidence_level: number
  notes: string
  recorded_by: string
  recorded_by_name: string
  recorded_at: string
  is_estimated: boolean
  data_source?: string // Optional data source field
  source_data: Record<string, any>
}

export interface KPITarget {
  id: string
  kpi: string
  kpi_name: string
  target_value: number
  target_type: 'ABSOLUTE' | 'PERCENTAGE_CHANGE' | 'RELATIVE'
  start_date: string
  end_date: string
  department: string | null
  department_name: string
  project: string | null
  project_name: string
  employee: string | null
  employee_name: string
  description: string
  is_stretch_goal: boolean
  weight: number
  created_by: string
  created_by_name: string
  created_at: string
  updated_at: string
}

export interface KPIAlert {
  id: string
  kpi: string
  kpi_name: string
  alert_type: 'THRESHOLD_BREACH' | 'TARGET_MISSED' | 'TREND_CHANGE' | 'DATA_QUALITY' | 'MISSING_DATA'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  title: string
  title_ar: string
  message: string
  message_ar: string
  current_value: number | null
  threshold_value: number | null
  target_value: number | null
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED'
  acknowledged_by: string | null
  acknowledged_by_name: string
  acknowledged_at: string | null
  resolved_by: string | null
  resolved_by_name: string
  resolved_at: string | null
  resolution_notes: string
  notified_users: string[]
  notification_sent: boolean
  created_at: string
  updated_at: string
}

export interface KPIDashboard {
  categories: KPICategory[]
  recent_alerts: KPIAlert[]
  top_performing_kpis: KPI[]
  underperforming_kpis: KPI[]
  kpi_summary: {
    total_kpis: number
    active_kpis: number
    kpis_on_target: number
    kpis_above_target: number
    kpis_below_target: number
    active_alerts: number
    critical_alerts: number
    categories_count: number
    last_updated: string
  }
}

export interface KPITrendData {
  kpi_id: string
  kpi_name: string
  data_points: Array<{
    date: string
    value: number
    period_start: string
    period_end: string
  }>
  trend_direction: 'up' | 'down' | 'stable'
  change_percentage: number
}

class KPIService {
  private useMockData = false // Set to true for development without backend

  // Method to enable/disable mock data
  setMockDataMode(enabled: boolean) {
    this.useMockData = enabled
    console.log(`KPI Service: Mock data mode ${enabled ? 'enabled' : 'disabled'}`)
  }

  // Method to check if backend is available
  async checkBackendConnection(): Promise<boolean> {
    try {
      const response = await apiClient.get('/kpi/categories/')
      return response.status === 200
    } catch (error) {
      console.warn('Backend connection failed:', error)
      return false
    }
  }

  // Categories
  async getCategories(): Promise<KPICategory[]> {
    if (this.useMockData) {
      return this.getMockCategories()
    }

    try {
      const response = await apiClient.get<KPICategory[]>('/kpi/categories/')
      return response.data as KPICategory[]
    } catch (error) {
      console.error('Error fetching KPI categories:', error)
      // Only fall back to mock data if explicitly enabled or in development
      if (import.meta.env.DEV) {
        console.warn('Falling back to mock data for KPI categories')
        return this.getMockCategories()
      }
      throw error
    }
  }

  private getMockCategories(): KPICategory[] {
    return [
      {
        id: '1',
        name: 'Financial Performance',
        name_ar: 'الأداء المالي',
        description: 'Financial metrics and indicators',
        description_ar: 'المقاييس والمؤشرات المالية',
        icon: 'DollarSign',
        color: 'from-green-500 to-green-600',
        is_active: true,
        sort_order: 1,
        kpi_count: 5,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Operational Efficiency',
        name_ar: 'الكفاءة التشغيلية',
        description: 'Operational performance metrics',
        description_ar: 'مقاييس الأداء التشغيلي',
        icon: 'Settings',
        color: 'from-blue-500 to-blue-600',
        is_active: true,
        sort_order: 2,
        kpi_count: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]
  }

  async createCategory(data: Partial<KPICategory>): Promise<KPICategory> {
    try {
      const response = await apiClient.post('/kpi/categories/', data)
      return response.data as KPICategory
    } catch (error) {
      console.error('Error creating category:', error)
      // Return mock created category for development
      const newCategory: KPICategory = {
        id: Date.now().toString(),
        name: data.name || 'New Category',
        name_ar: data.name_ar || 'فئة جديدة',
        description: data.description || '',
        description_ar: data.description_ar || '',
        icon: data.icon || 'BarChart3',
        color: data.color || 'from-blue-500 to-blue-600',
        is_active: data.is_active ?? true,
        sort_order: data.sort_order || 1,
        kpi_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      return newCategory
    }
  }

  async updateCategory(id: string, data: Partial<KPICategory>): Promise<KPICategory> {
    try {
      const response = await apiClient.put(`/kpi/categories/${id}/`, data)
      return response.data as KPICategory
    } catch (error) {
      console.error('Error updating category:', error)
      // Return mock updated category for development
      const mockCategories = this.getMockCategories()
      const existingCategory = mockCategories.find(cat => cat.id === id)
      if (existingCategory) {
        return {
          ...existingCategory,
          ...data,
          updated_at: new Date().toISOString()
        }
      }
      throw new Error('Category not found')
    }
  }

  async deleteCategory(id: string): Promise<void> {
    try {
      await apiClient.delete(`/kpi/categories/${id}/`)
    } catch (error) {
      console.error('Error deleting category:', error)
      // For development, just log the deletion
      console.log(`Mock: Category ${id} deleted successfully`)
    }
  }

  async getCategoryKPIs(categoryId: string): Promise<KPI[]> {
    const response = await apiClient.get(`/kpi/categories/${categoryId}/kpis/`)
    return response.data as KPI[]
  }

  // KPIs
  async getKPIs(params?: {
    status?: string
    category?: string
    limit?: number
    offset?: number
  }): Promise<KPI[]> {
    if (this.useMockData) {
      return this.getMockKPIs()
    }

    try {
      const response = await apiClient.get<KPI[]>('/kpi/kpis/', { params })
      return response.data as KPI[]
    } catch (error) {
      console.error('Error fetching KPIs:', error)
      // Only fall back to mock data if explicitly enabled or in development
      if (import.meta.env.DEV) {
        console.warn('Falling back to mock data for KPIs')
        return this.getMockKPIs()
      }
      throw error
    }
  }

  private getMockKPIs(): KPI[] {
    return [
      {
        id: '1',
        name: 'Revenue Growth',
        name_ar: 'نمو الإيرادات',
        description: 'Monthly revenue growth percentage',
        description_ar: 'نسبة نمو الإيرادات الشهرية',
        category: '1',
        category_name: 'Financial Performance',
        category_name_ar: 'الأداء المالي',
        measurement_type: 'PERCENTAGE',
        unit: '%',
        unit_ar: '%',
        frequency: 'MONTHLY',
        trend_direction: 'UP',
        formula: '((Current Month Revenue - Previous Month Revenue) / Previous Month Revenue) * 100',
        data_source: 'Financial System',
        calculation_method: 'Automatic',
        target_value: 15,
        warning_threshold: 10,
        critical_threshold: 5,
        visible_to_roles: ['admin', 'finance'],
        owner: 'user1',
        owner_name: 'Finance Manager',
        status: 'ACTIVE',
        is_automated: true,
        automation_config: {},
        created_by: 'user1',
        created_by_name: 'Admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        current_value: {
          value: 12.5,
          period_start: '2024-01-01',
          period_end: '2024-01-31',
          recorded_at: new Date().toISOString()
        },
        target_achievement: 83.3,
        trend: {
          change_percentage: 2.1,
          direction: 'up'
        }
      },
      {
        id: '2',
        name: 'Customer Satisfaction',
        name_ar: 'رضا العملاء',
        description: 'Customer satisfaction score',
        description_ar: 'نقاط رضا العملاء',
        category: '2',
        category_name: 'Operational Efficiency',
        category_name_ar: 'الكفاءة التشغيلية',
        measurement_type: 'SCORE',
        unit: 'points',
        unit_ar: 'نقاط',
        frequency: 'WEEKLY',
        trend_direction: 'UP',
        formula: 'Average of customer satisfaction surveys',
        data_source: 'CRM System',
        calculation_method: 'Manual',
        target_value: 4.5,
        warning_threshold: 4.0,
        critical_threshold: 3.5,
        visible_to_roles: ['admin', 'sales', 'support'],
        owner: 'user2',
        owner_name: 'Customer Success Manager',
        status: 'ACTIVE',
        is_automated: false,
        automation_config: {},
        created_by: 'user1',
        created_by_name: 'Admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        current_value: {
          value: 4.2,
          period_start: '2024-01-22',
          period_end: '2024-01-28',
          recorded_at: new Date().toISOString()
        },
        target_achievement: 93.3,
        trend: {
          change_percentage: -1.2,
          direction: 'down'
        }
      }
    ]
  }

  async getKPI(id: string): Promise<KPI> {
    if (this.useMockData) {
      const mockKPIs = this.getMockKPIs()
      const kpi = mockKPIs.find(k => k.id === id)
      if (!kpi) {
        throw new Error('KPI not found')
      }
      return kpi
    }

    try {
      const response = await apiClient.get(`/kpi/kpis/${id}/`)
      return response.data as KPI
    } catch (error) {
      console.error('Error fetching KPI:', error)
      if (import.meta.env.DEV) {
        console.warn('Falling back to mock data for KPI')
        const mockKPIs = this.getMockKPIs()
        const kpi = mockKPIs.find(k => k.id === id)
        if (!kpi) {
          throw new Error('KPI not found')
        }
        return kpi
      }
      throw error
    }
  }

  async createKPI(data: Partial<KPI>): Promise<KPI> {
    if (this.useMockData) {
      return this.createMockKPI(data)
    }

    try {
      const response = await apiClient.post('/kpi/kpis/', data)
      return response.data as KPI
    } catch (error) {
      console.error('Error creating KPI:', error)
      // Only fall back to mock data if explicitly enabled or in development
      if (import.meta.env.DEV) {
        console.warn('Falling back to mock data for KPI creation')
        return this.createMockKPI(data)
      }
      throw error
    }
  }

  private createMockKPI(data: Partial<KPI>): KPI {
    return {
      id: Date.now().toString(),
      name: data.name || 'New KPI',
      name_ar: data.name_ar || 'مؤشر جديد',
      description: data.description || '',
      description_ar: data.description_ar || '',
      category: data.category || '1',
      category_name: 'General',
      category_name_ar: 'عام',
      measurement_type: data.measurement_type || 'NUMBER',
      unit: data.unit || '',
      unit_ar: data.unit_ar || '',
      frequency: data.frequency || 'MONTHLY',
      trend_direction: data.trend_direction || 'UP',
      formula: data.formula || '',
      data_source: data.data_source || '',
      calculation_method: 'Manual',
      target_value: data.target_value || 0,
      warning_threshold: data.warning_threshold || 0,
      critical_threshold: data.critical_threshold || 0,
      visible_to_roles: ['admin'],
      owner: 'user1',
      owner_name: 'Current User',
      status: 'ACTIVE',
      is_automated: false,
      automation_config: {},
      created_by: 'user1',
      created_by_name: 'Current User',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      current_value: {
        value: 0,
        period_start: new Date().toISOString().split('T')[0],
        period_end: new Date().toISOString().split('T')[0],
        recorded_at: new Date().toISOString()
      },
      target_achievement: 0,
      trend: {
        change_percentage: 0,
        direction: 'stable' as const
      }
    }
  }

  async updateKPI(id: string, data: Partial<KPI>): Promise<KPI> {
    if (this.useMockData) {
      // Return updated mock KPI
      const mockKPIs = this.getMockKPIs()
      const existingKPI = mockKPIs.find(kpi => kpi.id === id)
      if (existingKPI) {
        return {
          ...existingKPI,
          ...data,
          updated_at: new Date().toISOString()
        }
      }
      throw new Error('KPI not found')
    }

    try {
      const response = await apiClient.put(`/kpi/kpis/${id}/`, data)
      return response.data as KPI
    } catch (error) {
      console.error('Error updating KPI:', error)
      // Only fall back to mock data if explicitly enabled or in development
      if (import.meta.env.DEV) {
        console.warn('Falling back to mock data for KPI update')
        const mockKPIs = this.getMockKPIs()
        const existingKPI = mockKPIs.find(kpi => kpi.id === id)
        if (existingKPI) {
          return {
            ...existingKPI,
            ...data,
            updated_at: new Date().toISOString()
          }
        }
      }
      throw error
    }
  }

  async deleteKPI(id: string): Promise<void> {
    if (this.useMockData) {
      // Mock deletion - just return success
      console.log(`Mock: KPI ${id} deleted successfully`)
      return Promise.resolve()
    }

    try {
      await apiClient.delete(`/kpi/kpis/${id}/`)
    } catch (error) {
      console.error('Error deleting KPI:', error)
      if (import.meta.env.DEV) {
        console.warn('Mock deletion for development')
        console.log(`Mock: KPI ${id} deleted successfully`)
        return Promise.resolve()
      }
      throw error
    }
  }

  async getKPIValues(kpiId: string, params?: {
    start_date?: string
    end_date?: string
    limit?: number
  }): Promise<KPIValue[]> {
    if (this.useMockData) {
      return this.getMockKPIValues(kpiId)
    }

    try {
      const response = await apiClient.get(`/kpi/kpis/${kpiId}/values/`, { params })
      return response.data as KPIValue[]
    } catch (error) {
      console.error('Error fetching KPI values:', error)
      if (import.meta.env.DEV) {
        console.warn('Falling back to mock data for KPI values')
        return this.getMockKPIValues(kpiId)
      }
      throw error
    }
  }

  private getMockKPIValues(kpiId: string): KPIValue[] {
    // Generate mock historical values
    const values: KPIValue[] = []
    const now = new Date()

    for (let i = 0; i < 12; i++) {
      const date = new Date(now)
      date.setMonth(date.getMonth() - i)

      values.push({
        id: `value-${kpiId}-${i}`,
        kpi: kpiId,
        kpi_name: 'Mock KPI',
        kpi_unit: 'units',
        value: 80 + Math.random() * 40, // Random value between 80-120
        period_start: date.toISOString().split('T')[0],
        period_end: date.toISOString().split('T')[0],
        department: null,
        department_name: '',
        project: null,
        project_name: '',
        employee: null,
        employee_name: '',
        data_quality_score: 100,
        confidence_level: 100,
        notes: '',
        recorded_by: 'user1',
        recorded_by_name: 'System',
        recorded_at: date.toISOString(),
        is_estimated: false,
        data_source: 'System Generated',
        source_data: {}
      })
    }

    return values.reverse() // Return in chronological order
  }

  async getKPITrend(kpiId: string, days: number = 30): Promise<KPITrendData> {
    if (this.useMockData) {
      return this.getMockKPITrend(kpiId, days)
    }

    try {
      const response = await apiClient.get(`/kpi/kpis/${kpiId}/trend/`, { params: { days } })
      return response.data as KPITrendData
    } catch (error) {
      console.error('Error fetching KPI trend:', error)
      if (import.meta.env.DEV) {
        console.warn('Falling back to mock data for KPI trend')
        return this.getMockKPITrend(kpiId, days)
      }
      throw error
    }
  }

  private getMockKPITrend(kpiId: string, days: number): KPITrendData {
    const mockKPIs = this.getMockKPIs()
    const kpi = mockKPIs.find(k => k.id === kpiId)
    const kpiName = kpi?.name || 'Unknown KPI'

    // Generate mock data points
    const dataPoints = []
    const now = new Date()
    const baseValue = 100
    let currentValue = baseValue

    for (let i = days; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)

      // Add some random variation with a slight upward trend
      const randomChange = (Math.random() - 0.3) * 5
      currentValue = Math.max(0, currentValue + randomChange)

      dataPoints.push({
        date: date.toISOString().split('T')[0],
        value: currentValue,
        period_start: date.toISOString(),
        period_end: date.toISOString()
      })
    }

    // Calculate trend
    const firstValue = dataPoints[0].value
    const lastValue = dataPoints[dataPoints.length - 1].value
    const changePercentage = ((lastValue - firstValue) / firstValue) * 100

    let trendDirection: 'up' | 'down' | 'stable' = 'stable'
    if (changePercentage > 1) {
      trendDirection = 'up'
    } else if (changePercentage < -1) {
      trendDirection = 'down'
    }

    return {
      kpi_id: kpiId,
      kpi_name: kpiName,
      data_points: dataPoints,
      trend_direction: trendDirection,
      change_percentage: parseFloat(changePercentage.toFixed(2))
    }
  }

  async addKPIValue(kpiId: string, data: Partial<KPIValue>): Promise<KPIValue> {
    try {
      // Validate required fields
      if (!data.value && data.value !== 0) {
        throw new Error('Value is required')
      }
      if (!data.period_start) {
        throw new Error('Period start date is required')
      }
      if (!data.period_end) {
        throw new Error('Period end date is required')
      }

      const response = await apiClient.post(`/kpi/kpis/${kpiId}/add_value/`, data)
      return response.data as KPIValue
    } catch (error: any) {
      console.error('Error adding KPI value:', error)

      // Handle specific API errors
      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided')
      } else if (error.response?.status === 404) {
        throw new Error('KPI not found')
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to add values to this KPI')
      }

      throw error
    }
  }

  async updateKPIValue(kpiId: string, valueId: string, data: Partial<KPIValue>): Promise<KPIValue> {
    try {
      const response = await apiClient.put(`/kpi/kpis/${kpiId}/values/${valueId}/`, data)
      return response.data as KPIValue
    } catch (error: any) {
      console.error('Error updating KPI value:', error)

      if (error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided')
      } else if (error.response?.status === 404) {
        throw new Error('KPI value not found')
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this KPI value')
      }

      throw error
    }
  }

  async deleteKPIValue(kpiId: string, valueId: string): Promise<void> {
    try {
      await apiClient.delete(`/kpi/kpis/${kpiId}/values/${valueId}/`)
    } catch (error: any) {
      console.error('Error deleting KPI value:', error)

      if (error.response?.status === 404) {
        throw new Error('KPI value not found')
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to delete this KPI value')
      }

      throw error
    }
  }

  // Duplicate function implementations removed - using original implementations above

  // Alias for addKPIValue for backward compatibility
  async addValue(kpiId: string, data: Partial<KPIValue>): Promise<KPIValue> {
    return this.addKPIValue(kpiId, data)
  }

  // Values
  async getValues(params?: {
    kpi?: string
    start_date?: string
    end_date?: string
    limit?: number
    offset?: number
  }): Promise<KPIValue[]> {
    const response = await apiClient.get('/kpi/values/', { params })
    return response.data as KPIValue[]
  }

  async createValue(data: Partial<KPIValue>): Promise<KPIValue> {
    const response = await apiClient.post('/kpi/values/', data)
    return response.data as KPIValue
  }

  async updateValue(id: string, data: Partial<KPIValue>): Promise<KPIValue> {
    const response = await apiClient.put(`/kpi/values/${id}/`, data)
    return response.data as KPIValue
  }

  async deleteValue(id: string): Promise<void> {
    await apiClient.delete(`/kpi/values/${id}/`)
  }

  // Targets
  async getTargets(params?: {
    kpi?: string
    active_only?: boolean
    limit?: number
    offset?: number
  }): Promise<KPITarget[]> {
    try {
      const response = await apiClient.get<KPITarget[]>('/kpi/targets/', { params })
      return response.data as KPITarget[]
    } catch (error) {
      console.error('Error fetching KPI targets:', error)
      // Return mock data for development
      return this.getMockTargets()
    }
  }

  private getMockTargets(): KPITarget[] {
    return [
      {
        id: '1',
        kpi: '1',
        kpi_name: 'Revenue Growth',
        target_value: 15,
        target_type: 'PERCENTAGE_CHANGE',
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        department: null,
        department_name: '',
        project: null,
        project_name: '',
        employee: null,
        employee_name: '',
        description: 'Annual revenue growth target',
        is_stretch_goal: false,
        weight: 1.0,
        created_by: 'user1',
        created_by_name: 'Admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2',
        kpi: '2',
        kpi_name: 'Customer Satisfaction',
        target_value: 4.5,
        target_type: 'ABSOLUTE',
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        department: null,
        department_name: '',
        project: null,
        project_name: '',
        employee: null,
        employee_name: '',
        description: 'Annual customer satisfaction target',
        is_stretch_goal: false,
        weight: 1.0,
        created_by: 'user1',
        created_by_name: 'Admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]
  }

  async createTarget(data: Partial<KPITarget>): Promise<KPITarget> {
    const response = await apiClient.post('/kpi/targets/', data)
    return response.data as KPITarget
  }

  async updateTarget(id: string, data: Partial<KPITarget>): Promise<KPITarget> {
    const response = await apiClient.put(`/kpi/targets/${id}/`, data)
    return response.data as KPITarget
  }

  async deleteTarget(id: string): Promise<void> {
    await apiClient.delete(`/kpi/targets/${id}/`)
  }

  // Alerts
  async getAlerts(params?: {
    status?: string
    severity?: string
    limit?: number
    offset?: number
  }): Promise<KPIAlert[]> {
    try {
      const response = await apiClient.get<KPIAlert[]>('/kpi/alerts/', { params })
      return response.data
    } catch (error) {
      console.error('Error fetching KPI alerts:', error)
      // Return mock data for development
      return this.getMockAlerts()
    }
  }

  private getMockAlerts(): KPIAlert[] {
    return [
      {
        id: '1',
        kpi: '1',
        kpi_name: 'Revenue Growth',
        alert_type: 'THRESHOLD_BREACH',
        severity: 'MEDIUM',
        title: 'Revenue Growth Below Warning Threshold',
        title_ar: 'نمو الإيرادات أقل من عتبة التحذير',
        message: 'Revenue growth is below the warning threshold of 10%',
        message_ar: 'نمو الإيرادات أقل من عتبة التحذير البالغة 10%',
        current_value: 8.5,
        threshold_value: 10,
        target_value: 15,
        status: 'ACTIVE',
        acknowledged_by: null,
        acknowledged_by_name: '',
        acknowledged_at: null,
        resolved_by: null,
        resolved_by_name: '',
        resolved_at: null,
        resolution_notes: '',
        notified_users: ['user1', 'user2'],
        notification_sent: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]
  }

  async acknowledgeAlert(id: string): Promise<KPIAlert> {
    const response = await apiClient.post(`/kpi/alerts/${id}/acknowledge/`)
    return response.data as KPIAlert
  }

  async resolveAlert(id: string, resolution_notes?: string): Promise<KPIAlert> {
    const response = await apiClient.post(`/kpi/alerts/${id}/resolve/`, { resolution_notes })
    return response.data as KPIAlert
  }

  // Dashboard
  async getDashboard(params?: {
    start_date?: string
    end_date?: string
    compare_start_date?: string
    compare_end_date?: string
  }): Promise<KPIDashboard> {
    if (this.useMockData) {
      return this.getMockDashboard()
    }

    try {
      const response = await apiClient.get<KPIDashboard>('/kpi/dashboard/', { params })
      return response.data as KPIDashboard
    } catch (error) {
      console.error('Error fetching KPI dashboard:', error)
      // Only fall back to mock data if explicitly enabled or in development
      if (import.meta.env.DEV) {
        console.warn('Falling back to mock data for KPI dashboard')
        return this.getMockDashboard()
      }
      throw error
    }
  }

  private getMockDashboard(): KPIDashboard {
    const mockKPIs = this.getMockKPIs()
    const mockCategories = this.getMockCategories()

    return {
      categories: mockCategories,
      recent_alerts: [
        {
          id: '1',
          kpi: '1',
          kpi_name: 'Revenue Growth',
          alert_type: 'THRESHOLD_BREACH',
          severity: 'MEDIUM',
          title: 'Revenue Growth Below Warning Threshold',
          title_ar: 'نمو الإيرادات أقل من عتبة التحذير',
          message: 'Revenue growth is below the warning threshold of 10%',
          message_ar: 'نمو الإيرادات أقل من عتبة التحذير البالغة 10%',
          current_value: 8.5,
          threshold_value: 10,
          target_value: 15,
          status: 'ACTIVE',
          acknowledged_by: null,
          acknowledged_by_name: '',
          acknowledged_at: null,
          resolved_by: null,
          resolved_by_name: '',
          resolved_at: null,
          resolution_notes: '',
          notified_users: ['user1', 'user2'],
          notification_sent: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ],
      top_performing_kpis: [mockKPIs[1]], // Customer Satisfaction
      underperforming_kpis: [mockKPIs[0]], // Revenue Growth
      kpi_summary: {
        total_kpis: 2,
        active_kpis: 2,
        kpis_on_target: 1,
        kpis_above_target: 0,
        kpis_below_target: 1,
        active_alerts: 1,
        critical_alerts: 0,
        categories_count: 2,
        last_updated: new Date().toISOString()
      }
    }
  }

  async getRoleBasedKPIs(): Promise<{
    role: string
    accessible_kpis: KPI[]
    role_specific_metrics: Record<string, number>
    alerts: KPIAlert[]
  }> {
    const response = await apiClient.get('/kpi/role-based/')
    return response.data as {
      role: string
      accessible_kpis: KPI[]
      role_specific_metrics: Record<string, number>
      alerts: KPIAlert[]
    }
  }
}

export default new KPIService()
