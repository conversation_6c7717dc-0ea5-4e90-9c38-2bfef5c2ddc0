INFO 2025-07-16 04:55:25,396 autoreload 60917 8360664832 Watching for file changes with StatReloader
WARNING 2025-07-16 04:55:38,440 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:55:38,441 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:56:08,396 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:56:08,397 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:56:38,405 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:56:38,407 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:57:08,396 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:57:08,396 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:57:38,396 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:57:38,396 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:58:08,398 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:58:08,399 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:58:38,395 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:58:38,396 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:59:08,580 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:59:08,582 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:59:38,399 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:59:38,400 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:00:08,407 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:00:08,408 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:00:38,428 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:00:38,429 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:01:08,407 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:01:08,408 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:01:38,405 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:01:38,406 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:02:08,401 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:02:08,404 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:02:38,401 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:02:38,403 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:02:40,705 basehttp 60917 6173765632 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:40,706 middleware 60917 6173765632 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 05:02:40,958 basehttp 60917 6173765632 "POST /api/auth/login/ HTTP/1.1" 200 959
INFO 2025-07-16 05:02:40,996 basehttp 60917 6173765632 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:02:40,996 basehttp 60917 6190592000 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:41,004 basehttp 60917 6190592000 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:41,014 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:02:41,020 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:02:41,022 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:02:41,030 basehttp 60917 6190592000 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:02:41,367 basehttp 60917 6190592000 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:41,367 basehttp 60917 6207418368 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:41,423 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:02:41,429 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
WARNING 2025-07-16 05:03:08,413 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:03:08,414 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:03:11,014 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:03:37,823 basehttp 60917 6156939264 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:03:37,823 basehttp 60917 6173765632 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:03:37,839 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:03:37,843 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:03:37,878 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:03:37,880 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:03:37,887 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:03:37,889 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:03:38,192 basehttp 60917 6173765632 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 05:03:38,204 basehttp 60917 6173765632 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
WARNING 2025-07-16 05:03:38,399 log 60917 6207418368 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:03:38,399 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:04:07,884 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:04:08,399 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:04:08,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:04:37,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:04:38,400 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:04:38,400 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:05:07,879 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:05:08,398 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:05:08,398 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:05:37,883 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:05:38,396 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:05:38,396 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:06:07,880 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:06:08,397 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:06:08,397 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:06:37,881 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:06:38,398 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:06:38,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:07:07,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:07:08,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:07:08,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:07:37,887 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:07:38,400 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:07:38,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:08:07,878 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:08:08,399 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:08:08,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:08:37,882 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:08:38,400 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:08:38,403 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:08:38,412 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 05:09:07,884 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:09:08,399 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:09:08,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:09:37,887 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:09:38,404 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:09:38,404 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:10:07,885 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:10:08,399 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:10:08,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:10:37,892 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:10:38,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:10:38,402 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:11:07,886 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:11:08,400 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:11:08,400 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:11:37,889 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:11:38,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:11:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:12:07,891 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:12:08,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:12:08,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:12:37,905 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:12:38,404 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:12:38,404 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:13:07,902 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:13:08,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:13:08,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:13:37,891 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:13:38,402 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:13:38,402 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:13:38,574 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:14:07,896 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:14:08,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:14:08,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:14:37,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:14:38,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:14:38,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:15:07,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:15:08,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:15:08,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:15:37,926 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:15:38,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:15:38,407 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:16:07,906 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:16:08,411 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:16:08,411 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:16:37,891 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:16:38,403 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:16:38,403 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:17:07,893 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:17:08,402 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:17:08,402 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:17:37,921 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:17:38,431 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:17:38,432 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:18:07,891 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:18:08,403 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:18:08,403 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:18:37,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:18:38,402 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:18:38,402 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:18:38,796 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:19:07,889 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:19:08,403 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:19:08,403 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:19:37,898 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:19:38,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:19:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:20:07,904 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:20:08,404 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:20:08,404 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:20:37,922 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:20:38,408 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:20:38,408 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:21:07,894 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:21:08,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:21:08,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:21:37,906 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:21:38,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:21:38,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:22:07,893 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:22:08,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:22:08,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:22:37,897 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:22:38,404 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:22:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:23:07,895 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:23:08,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:23:08,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:23:37,897 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:23:38,407 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:23:38,407 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:23:38,963 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:24:07,894 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:24:08,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:24:08,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:24:37,895 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:24:38,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:24:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:25:07,898 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:25:08,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:25:08,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:25:37,894 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:25:38,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:25:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:07,898 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:26:08,409 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:26:08,411 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:37,894 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:26:38,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:26:38,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,223 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:26:51,247 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:26:51,296 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:26:51,297 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:26:51,312 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:26:51,312 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:26:51,530 log 60917 6207418368 Unauthorized: /api/auth/user/
WARNING 2025-07-16 05:26:51,530 basehttp 60917 6207418368 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,545 basehttp 60917 6207418368 "OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:26:51,547 log 60917 6224244736 Unauthorized: /api/auth/user/
WARNING 2025-07-16 05:26:51,547 basehttp 60917 6224244736 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,550 middleware 60917 6207418368 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 05:26:51,557 middleware 60917 6224244736 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 05:26:51,564 auth_views 60917 6207418368 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 05:26:51,564 log 60917 6207418368 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 05:26:51,565 basehttp 60917 6207418368 "POST /api/auth/refresh/ HTTP/1.1" 500 52
ERROR 2025-07-16 05:26:51,568 auth_views 60917 6224244736 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 05:26:51,568 log 60917 6224244736 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 05:26:51,568 basehttp 60917 6224244736 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 05:26:51,571 basehttp 60917 6207418368 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
INFO 2025-07-16 05:26:51,573 basehttp 60917 6224244736 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:26:51,575 log 60917 6207418368 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 05:26:51,575 basehttp 60917 6207418368 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:26:51,578 log 60917 6224244736 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 05:26:51,578 basehttp 60917 6224244736 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,580 basehttp 60917 6241071104 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
INFO 2025-07-16 05:26:51,580 basehttp 60917 6257897472 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:26:51,586 log 60917 6257897472 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 05:26:51,587 log 60917 6241071104 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 05:26:51,588 basehttp 60917 6257897472 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:26:51,589 basehttp 60917 6241071104 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,615 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:26:51,638 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:27:21,298 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:27:51,296 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:28:21,274 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:28:51,279 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:29:21,271 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:29:51,269 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:12,866 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:30:12,882 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:30:12,934 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:12,955 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:30:12,963 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:13,043 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:30:13,250 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:30:13,263 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:30:25,895 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:30:25,916 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:30:25,960 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:25,976 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:30:26,000 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:26,007 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:30:26,353 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:30:26,382 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:30:55,986 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:31:25,979 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:31:45,529 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:31:45,547 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:31:45,636 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:31:45,687 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:31:45,736 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:31:45,765 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:31:45,991 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:31:46,014 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:15,632 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:21,789 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:21,810 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:21,846 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:21,866 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:21,888 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:21,905 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:22,242 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:22,263 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:35,517 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:35,549 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:35,596 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:35,840 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:35,859 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:35,914 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:36,355 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:36,365 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:48,359 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:48,376 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:48,685 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:48,723 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:48,743 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:48,798 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:48,810 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:48,830 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:59,222 basehttp 60917 6190592000 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:59,257 basehttp 60917 6190592000 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:59,294 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:59,305 basehttp 60917 6190592000 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:59,314 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:59,349 basehttp 60917 6190592000 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:59,633 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:59,755 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:33:29,290 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:33:59,294 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:34:29,292 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:34:59,293 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:35:29,291 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:35:59,295 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:19,555 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:19,573 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:19,640 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:19,656 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:19,664 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:19,679 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:19,952 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:36:19,964 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:36:31,098 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:31,115 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:31,155 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:31,158 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:31,230 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:31,240 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:31,565 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:36:31,590 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 220
INFO 2025-07-16 05:36:39,576 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:39,593 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:39,668 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:39,678 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:39,694 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:39,706 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:40,004 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:36:40,023 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:37:09,647 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:37:39,646 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:38:09,646 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:38:39,648 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:09,654 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:39,645 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:57,106 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:39:57,117 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:39:57,196 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:57,226 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:39:57,265 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:57,277 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:39:57,554 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:39:57,596 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:40:27,178 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:40:57,178 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:41:27,186 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:41:57,182 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:42:27,185 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:42:57,183 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:43:05,301 autoreload 65504 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 05:43:27,184 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:44:31,963 basehttp 60917 6156939264 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:44:31,972 basehttp 60917 6173765632 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:44:31,993 log 60917 6156939264 Unauthorized: /api/auth/user/
WARNING 2025-07-16 05:44:31,994 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:44:31,998 log 60917 6173765632 Unauthorized: /api/auth/user/
WARNING 2025-07-16 05:44:31,999 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 05:46:04,466 basehttp 60917 6156939264 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,468 middleware 60917 6156939264 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 05:46:04,700 basehttp 60917 6156939264 "POST /api/auth/login/ HTTP/1.1" 200 959
INFO 2025-07-16 05:46:04,728 basehttp 60917 6173765632 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,729 basehttp 60917 6156939264 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,730 basehttp 60917 6190592000 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,731 basehttp 60917 6173765632 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,740 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:46:04,740 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:04,748 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:04,759 basehttp 60917 6190592000 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:46:05,073 basehttp 60917 6190592000 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:05,074 basehttp 60917 6173765632 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:05,079 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:46:05,086 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 05:46:25,629 basehttp 60917 6190592000 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:46:25,629 basehttp 60917 6173765632 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:46:25,651 basehttp 60917 6190592000 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:46:25,662 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:46:25,705 basehttp 60917 6173765632 "OPTIONS /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:46:25,711 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:46:34,737 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:44,878 basehttp 60917 6173765632 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:44,881 basehttp 60917 6190592000 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:44,886 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:46:44,891 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:46:44,926 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:44,931 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:46:44,934 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:44,937 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:46:45,233 basehttp 60917 6173765632 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:46:45,249 basehttp 60917 6173765632 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:47:14,929 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:47:44,109 basehttp 60917 6156939264 "OPTIONS /api/kpis/?page=1&page_size=20 HTTP/1.1" 200 0
INFO 2025-07-16 05:47:44,109 basehttp 60917 6173765632 "OPTIONS /api/kpis/?page=1&page_size=20 HTTP/1.1" 200 0
INFO 2025-07-16 05:47:44,109 basehttp 60917 6207418368 "OPTIONS /api/kpi-dashboard/ HTTP/1.1" 200 0
INFO 2025-07-16 05:47:44,109 basehttp 60917 6190592000 "OPTIONS /api/kpi-dashboard/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:47:44,113 log 60917 6190592000 Not Found: /api/kpi-dashboard/
WARNING 2025-07-16 05:47:44,114 log 60917 6207418368 Not Found: /api/kpis/
WARNING 2025-07-16 05:47:44,114 basehttp 60917 6207418368 "GET /api/kpis/?page=1&page_size=20 HTTP/1.1" 404 179
WARNING 2025-07-16 05:47:44,114 basehttp 60917 6190592000 "GET /api/kpi-dashboard/ HTTP/1.1" 404 179
WARNING 2025-07-16 05:47:44,117 log 60917 6190592000 Not Found: /api/kpi-dashboard/
WARNING 2025-07-16 05:47:44,118 log 60917 6207418368 Not Found: /api/kpis/
WARNING 2025-07-16 05:47:44,118 basehttp 60917 6190592000 "GET /api/kpi-dashboard/ HTTP/1.1" 404 179
WARNING 2025-07-16 05:47:44,118 basehttp 60917 6207418368 "GET /api/kpis/?page=1&page_size=20 HTTP/1.1" 404 179
INFO 2025-07-16 05:47:44,928 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:48:14,938 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:50:26,110 basehttp 60917 6156939264 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,113 basehttp 60917 6173765632 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,159 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:50:26,165 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:50:26,194 basehttp 60917 6156939264 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,195 basehttp 60917 6173765632 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,198 basehttp 60917 6156939264 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,199 basehttp 60917 6190592000 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,202 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:50:26,206 basehttp 60917 6207418368 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:50:26,209 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:50:26,212 basehttp 60917 6207418368 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:50:26,511 basehttp 60917 6207418368 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,511 basehttp 60917 6173765632 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,518 basehttp 60917 6207418368 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 05:50:26,529 basehttp 60917 6207418368 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:50:56,214 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:10,254 basehttp 60917 6173765632 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:51:10,254 basehttp 60917 6207418368 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:51:10,279 basehttp 60917 6207418368 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:51:10,293 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:51:10,333 basehttp 60917 6173765632 "OPTIONS /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:51:10,340 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:51:26,210 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:36,851 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:51:56,204 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:58,100 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:51:58,112 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:51:58,136 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:51:58,136 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:58,151 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:58,152 basehttp 60917 6207418368 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:51:58,456 basehttp 60917 6207418368 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:51:58,466 basehttp 60917 6207418368 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:57:29,159 autoreload 60917 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 05:57:29,159 autoreload 65504 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 05:57:29,666 autoreload 66063 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 05:57:29,782 autoreload 66062 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:01:42,060 autoreload 66342 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:02:28,280 autoreload 66417 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:03:01,168 autoreload 66508 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:04:52,573 basehttp 66508 6156185600 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:52,576 middleware 66508 6156185600 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 06:04:52,826 basehttp 66508 6156185600 "POST /api/auth/login/ HTTP/1.1" 200 959
INFO 2025-07-16 06:04:52,854 basehttp 66508 6156185600 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 06:04:52,857 basehttp 66508 6173011968 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:52,857 basehttp 66508 6189838336 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:52,864 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:04:52,869 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:04:52,872 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:04:52,877 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:04:53,237 basehttp 66508 6156185600 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:53,237 basehttp 66508 6173011968 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:53,258 basehttp 66508 6156185600 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 06:04:53,263 basehttp 66508 6156185600 "GET /api/dashboard-stats/ HTTP/1.1" 200 220
INFO 2025-07-16 06:05:17,579 basehttp 66508 6156185600 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:05:17,580 basehttp 66508 6173011968 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:05:17,610 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:05:17,621 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:05:17,668 basehttp 66508 6173011968 "OPTIONS /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:05:17,675 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:05:22,865 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:05:30,664 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:05:52,882 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:06:22,878 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:06:52,876 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:07:06,515 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:22,868 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:07:25,772 basehttp 66508 6173011968 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 06:07:25,773 basehttp 66508 6156185600 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 06:07:25,784 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:07:25,790 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:07:25,817 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:07:25,820 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:07:25,824 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:07:25,826 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:07:26,121 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:26,129 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:26,196 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:48,880 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:55,837 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:08:25,847 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:08:55,837 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:09:25,841 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:09:55,845 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:10:25,824 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:10:39,813 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:10:55,830 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:11:25,829 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:11:55,826 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:12:25,829 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:12:55,844 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:13:25,837 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:13:55,831 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:14:25,828 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:14:55,838 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:15:25,838 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:15:27,077 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:15:27,082 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:15:27,144 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:15:27,161 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:15:27,181 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:15:27,200 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:15:27,424 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9286
INFO 2025-07-16 06:15:27,431 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9286
INFO 2025-07-16 06:15:27,491 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9286
INFO 2025-07-16 06:15:57,136 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:16:27,136 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:16:49,326 basehttp 66508 6173011968 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:16:49,336 basehttp 66508 6173011968 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:16:49,362 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:16:49,364 basehttp 66508 6173011968 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:16:49,374 basehttp 66508 6189838336 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:16:49,375 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:16:49,666 basehttp 66508 6189838336 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:16:49,678 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:16:49,742 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:17:12,223 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:17:19,361 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:17:49,362 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:18:19,314 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:18:19,390 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:18:49,368 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:19:19,378 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:19:25,592 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:19:25,598 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:19:25,624 basehttp 66508 6189838336 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:19:25,628 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:19:25,628 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:19:25,632 basehttp 66508 6189838336 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:19:25,930 basehttp 66508 6189838336 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:19:25,942 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:19:26,000 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:19:55,645 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:20:25,639 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:20:55,638 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:21:25,662 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:21:39,756 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:21:39,761 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:21:39,796 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:21:39,799 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:21:39,808 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:21:39,811 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:21:40,099 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:21:40,109 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:21:40,169 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:22:09,804 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:22:39,803 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:23:09,802 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:23:39,814 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:24:09,805 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:24:39,804 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:25:09,813 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:25:39,811 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:26:09,813 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
ERROR 2025-07-16 06:26:19,194 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,420 log 68078 8360664832 Bad Request: /api/employees/
ERROR 2025-07-16 06:26:19,420 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,421 log 68078 8360664832 Bad Request: /api/departments/
ERROR 2025-07-16 06:26:19,421 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,421 log 68078 8360664832 Bad Request: /api/dashboard-stats/
ERROR 2025-07-16 06:26:19,421 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,421 log 68078 8360664832 Bad Request: /api/user-profile/
ERROR 2025-07-16 06:26:19,421 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,421 log 68078 8360664832 Bad Request: /api/activities/
ERROR 2025-07-16 06:26:19,422 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,422 log 68078 8360664832 Bad Request: /api/roles/
ERROR 2025-07-16 06:26:19,422 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,422 log 68078 8360664832 Bad Request: /api/user-profiles/
ERROR 2025-07-16 06:26:19,422 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,422 log 68078 8360664832 Bad Request: /api/performance-reviews/
ERROR 2025-07-16 06:26:19,422 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,423 log 68078 8360664832 Bad Request: /api/payroll/
ERROR 2025-07-16 06:26:19,423 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,423 log 68078 8360664832 Bad Request: /api/recruitment/
ERROR 2025-07-16 06:26:19,423 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,423 log 68078 8360664832 Bad Request: /api/training/
ERROR 2025-07-16 06:26:19,423 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,424 log 68078 8360664832 Bad Request: /api/invoices/
ERROR 2025-07-16 06:26:19,424 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,424 log 68078 8360664832 Bad Request: /api/cost-centers/
ERROR 2025-07-16 06:26:19,424 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,424 log 68078 8360664832 Bad Request: /api/export/employees/
ERROR 2025-07-16 06:26:19,424 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,424 log 68078 8360664832 Bad Request: /api/export/departments/
ERROR 2025-07-16 06:26:19,425 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,425 log 68078 8360664832 Bad Request: /api/export/attendance/
INFO 2025-07-16 06:26:39,813 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:26:41,850 autoreload 66062 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:26:41,867 autoreload 66508 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:26:42,258 autoreload 68125 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:26:42,382 autoreload 68124 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:27:09,827 basehttp 68125 6162182144 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
ERROR 2025-07-16 06:27:19,649 log 68155 8360664832 Internal Server Error: /api/export/departments/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend/ems/views.py", line 219, in department_export
    department.manager_ar or '',
AttributeError: 'Department' object has no attribute 'manager_ar'
INFO 2025-07-16 06:27:39,813 basehttp 68125 6162182144 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:28:09,813 basehttp 68125 6162182144 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:28:22,238 autoreload 68124 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:22,240 autoreload 68125 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:22,667 autoreload 68190 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:22,769 autoreload 68189 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:37,481 autoreload 68190 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:37,484 autoreload 68189 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:37,903 autoreload 68214 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:37,953 autoreload 68213 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:39,831 basehttp 68214 6159609856 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:28:53,639 autoreload 68213 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:53,778 autoreload 68214 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:54,079 autoreload 68231 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:54,116 autoreload 68232 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:29:09,824 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:29:15,112 basehttp 68232 6191001600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:29:15,119 basehttp 68232 6191001600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:29:15,156 basehttp 68232 6207827968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:29:15,158 basehttp 68232 6191001600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:29:15,160 basehttp 68232 6224654336 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:29:15,165 basehttp 68232 6191001600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:29:15,460 basehttp 68232 6191001600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:29:15,469 basehttp 68232 6224654336 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:29:15,523 basehttp 68232 6224654336 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:29:45,178 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:30:15,180 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:30:45,166 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:31:15,159 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:31:45,177 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:32:15,477 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:32:27,414 autoreload 68232 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:32:27,777 autoreload 68231 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:32:27,894 autoreload 68421 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:32:28,354 autoreload 68422 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:32:41,783 autoreload 68421 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:32:41,952 autoreload 68422 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:32:42,197 autoreload 68441 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:32:42,367 autoreload 68442 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:32:45,188 basehttp 68441 6168555520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:33:15,173 basehttp 68441 6168555520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:33:45,164 basehttp 68441 6168555520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:33:55,347 autoreload 68441 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:33:55,347 autoreload 68442 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:33:55,798 autoreload 68544 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:33:55,915 autoreload 68543 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:34:15,192 basehttp 68544 6160248832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:34:45,174 basehttp 68544 6160248832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:35:15,171 basehttp 68544 6160248832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:35:45,173 basehttp 68544 6160248832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:35:50,164 autoreload 68544 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/middleware.py changed, reloading.
INFO 2025-07-16 06:35:50,568 autoreload 68673 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:11,538 autoreload 68673 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:36:11,663 autoreload 68543 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:36:11,930 autoreload 68683 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:12,129 autoreload 68684 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:15,185 basehttp 68683 6166474752 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:36:44,227 autoreload 68684 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:36:44,229 autoreload 68683 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:36:44,652 autoreload 68714 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:44,716 autoreload 68713 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:45,178 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:37:15,166 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:37:45,171 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 06:38:02,026 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search='; DROP TABLE employees; --
WARNING 2025-07-16 06:38:02,026 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,026 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=1' UNION SELECT * FROM auth_user --
WARNING 2025-07-16 06:38:02,026 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,026 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=admin'; DELETE FROM employees WHERE '1'='1
WARNING 2025-07-16 06:38:02,027 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,029 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=<script>alert('XSS')</script>
WARNING 2025-07-16 06:38:02,029 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,029 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=javascript:alert('XSS')
WARNING 2025-07-16 06:38:02,029 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,029 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=<img src=x onerror=alert('XSS')>
WARNING 2025-07-16 06:38:02,030 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,030 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=eval('alert(1)')
WARNING 2025-07-16 06:38:02,030 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,030 log 68786 8360664832 Unauthorized: /api/employees/
WARNING 2025-07-16 06:38:02,031 log 68786 8360664832 Unauthorized: /api/departments/
WARNING 2025-07-16 06:38:02,031 log 68786 8360664832 Unauthorized: /api/dashboard-stats/
WARNING 2025-07-16 06:38:02,032 log 68786 8360664832 Unauthorized: /api/user-profile/
INFO 2025-07-16 06:38:02,032 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,033 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,033 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,138 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,139 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,139 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,242 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,243 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,244 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,350 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,351 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,351 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,457 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,458 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,459 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,560 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,561 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,561 log 68786 8360664832 Internal Server Error: /api/auth/login/
WARNING 2025-07-16 06:38:02,680 log 68786 8360664832 Unauthorized: /api/employees/
INFO 2025-07-16 06:38:15,162 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:38:45,169 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:39:15,169 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:39:20,498 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:20,885 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:21,114 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:21,260 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:22,081 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:22,314 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:22,890 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:24,071 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:24,938 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:50,022 basehttp 68714 6205026304 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,024 basehttp 68714 6221852672 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,058 basehttp 68714 6205026304 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:39:50,064 basehttp 68714 6205026304 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:39:50,094 basehttp 68714 6205026304 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,096 basehttp 68714 6221852672 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,099 basehttp 68714 6238679040 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,099 basehttp 68714 6255505408 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,103 basehttp 68714 6205026304 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:39:50,103 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:39:50,112 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:39:50,113 basehttp 68714 6205026304 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:39:50,397 basehttp 68714 6205026304 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,398 basehttp 68714 6221852672 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,412 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:50,445 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:50,480 basehttp 68714 6205026304 "OPTIONS /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,497 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:40:20,111 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:40:50,105 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:41:20,113 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:41:34,803 basehttp 68714 6221852672 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 06:41:34,803 basehttp 68714 6205026304 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 06:41:34,842 basehttp 68714 6221852672 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 06:41:45,972 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:41:45,979 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:41:46,007 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:41:46,010 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:41:46,014 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:41:46,017 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:41:46,302 basehttp 68714 6221852672 "OPTIONS /api/departments/?page=1&page_size=20 HTTP/1.1" 200 0
INFO 2025-07-16 06:41:46,302 basehttp 68714 6205026304 "OPTIONS /api/departments/?page=1&page_size=20 HTTP/1.1" 200 0
INFO 2025-07-16 06:41:46,324 basehttp 68714 6221852672 "GET /api/departments/?page=1&page_size=20 HTTP/1.1" 200 3425
INFO 2025-07-16 06:41:46,347 basehttp 68714 6221852672 "GET /api/departments/?page=1&page_size=20 HTTP/1.1" 200 3425
INFO 2025-07-16 06:42:16,022 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:42:46,031 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:43:16,027 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 06:43:19,252 log 69158 8360664832 Unauthorized: /api/employees/
WARNING 2025-07-16 06:43:19,258 middleware 69158 8360664832 Suspicious query parameter from 127.0.0.1: search='; DROP TABLE employees; --
WARNING 2025-07-16 06:43:19,258 log 69158 8360664832 Bad Request: /api/employees/
INFO 2025-07-16 06:43:46,014 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:44:16,010 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:44:46,024 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:06,222 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:45:06,276 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:45:06,291 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:06,298 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:45:06,301 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:06,306 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:45:36,302 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:50,440 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:45:50,446 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:45:50,474 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:50,475 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:45:50,483 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:50,484 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:45:50,805 basehttp 68714 6221852672 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:45:50,815 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:45:50,868 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:46:20,487 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:46:50,480 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:47:20,481 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
ERROR 2025-07-16 06:47:37,882 log 69300 8360664832 Internal Server Error: /api/employees/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'Customer Service'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "/Users/<USER>/Desktop/EMS/backend/ems/views.py", line 44, in get_queryset
    queryset = queryset.filter(department=department)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/sql/query.py", line 1545, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/sql/query.py", line 1576, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/sql/query.py", line 1491, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/sql/query.py", line 1318, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got 'Customer Service'.
INFO 2025-07-16 06:47:50,486 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:47:58,203 autoreload 68713 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:47:58,214 autoreload 68714 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:47:58,696 autoreload 69313 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:47:58,809 autoreload 69312 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:48:20,498 basehttp 69313 6129053696 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:48:26,729 autoreload 69312 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:48:26,769 autoreload 69313 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:48:27,136 autoreload 69333 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:48:27,172 autoreload 69332 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:48:50,515 basehttp 69333 6134099968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:48:55,254 autoreload 69332 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:48:55,289 autoreload 69333 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:48:55,788 autoreload 69367 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:48:55,823 autoreload 69366 8360664832 Watching for file changes with StatReloader
