#!/usr/bin/env python3
"""
Data Quality Analysis and Fixing Script for Numu EMS
Identifies and fixes data quality issues in the database
"""

import os
import sys
import django
from datetime import datetime, date
import re

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Employee, Department
from django.contrib.auth.models import User
from django.db import transaction

class DataQualityFixer:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
    
    def analyze_employee_data(self):
        """Analyze employee data for quality issues"""
        print("📊 ANALYZING EMPLOYEE DATA QUALITY")
        print("=" * 50)
        
        employees = Employee.objects.select_related('user', 'department').all()
        total_employees = employees.count()
        
        print(f"Total employees: {total_employees}")
        
        # Check for missing required fields
        missing_employee_id = employees.filter(employee_id__isnull=True).count()
        missing_position = employees.filter(position__isnull=True).count()
        missing_department = employees.filter(department__isnull=True).count()
        missing_hire_date = employees.filter(hire_date__isnull=True).count()
        
        print(f"\n🔍 Missing Data Analysis:")
        print(f"   • Missing Employee ID: {missing_employee_id}")
        print(f"   • Missing Position: {missing_position}")
        print(f"   • Missing Department: {missing_department}")
        print(f"   • Missing Hire Date: {missing_hire_date}")
        
        # Check for future hire dates
        future_hires = employees.filter(hire_date__gt=date.today()).count()
        print(f"   • Future Hire Dates: {future_hires}")
        
        # Check for duplicate employee IDs
        from django.db.models import Count
        duplicate_ids = Employee.objects.values('employee_id').annotate(
            count=Count('employee_id')
        ).filter(count__gt=1, employee_id__isnull=False)
        
        print(f"   • Duplicate Employee IDs: {duplicate_ids.count()}")
        
        # Check for inconsistent employment status
        invalid_status = employees.exclude(
            employment_status__in=['active', 'inactive', 'terminated', 'on_leave']
        ).count()
        print(f"   • Invalid Employment Status: {invalid_status}")
        
        return {
            'total': total_employees,
            'missing_employee_id': missing_employee_id,
            'missing_position': missing_position,
            'missing_department': missing_department,
            'missing_hire_date': missing_hire_date,
            'future_hires': future_hires,
            'duplicate_ids': duplicate_ids.count(),
            'invalid_status': invalid_status
        }
    
    def fix_missing_employee_ids(self):
        """Fix missing employee IDs by generating them"""
        print("\n🔧 FIXING MISSING EMPLOYEE IDs")
        print("-" * 30)
        
        employees_without_id = Employee.objects.filter(employee_id__isnull=True)
        
        if not employees_without_id.exists():
            print("✅ No missing employee IDs found")
            return
        
        # Get the highest existing employee ID number
        existing_ids = Employee.objects.filter(
            employee_id__isnull=False
        ).values_list('employee_id', flat=True)
        
        max_num = 0
        for emp_id in existing_ids:
            if emp_id and emp_id.startswith('EMP'):
                try:
                    num = int(emp_id[3:])
                    max_num = max(max_num, num)
                except ValueError:
                    continue
        
        with transaction.atomic():
            for i, employee in enumerate(employees_without_id, 1):
                new_id = f"EMP{max_num + i:03d}"
                employee.employee_id = new_id
                employee.save()
                print(f"   ✅ Generated ID {new_id} for {employee.user.get_full_name()}")
                self.fixes_applied.append(f"Generated employee ID: {new_id}")
    
    def fix_future_hire_dates(self):
        """Fix future hire dates by setting them to today or a reasonable past date"""
        print("\n🔧 FIXING FUTURE HIRE DATES")
        print("-" * 30)
        
        future_hires = Employee.objects.filter(hire_date__gt=date.today())
        
        if not future_hires.exists():
            print("✅ No future hire dates found")
            return
        
        with transaction.atomic():
            for employee in future_hires:
                old_date = employee.hire_date
                # Set to today or a reasonable past date
                employee.hire_date = date.today()
                employee.save()
                print(f"   ✅ Fixed hire date for {employee.user.get_full_name()}: {old_date} → {employee.hire_date}")
                self.fixes_applied.append(f"Fixed hire date for {employee.user.get_full_name()}")
    
    def fix_missing_positions(self):
        """Fix missing positions by assigning default positions"""
        print("\n🔧 FIXING MISSING POSITIONS")
        print("-" * 30)
        
        employees_without_position = Employee.objects.filter(
            position__isnull=True
        ).select_related('user', 'department')
        
        if not employees_without_position.exists():
            print("✅ No missing positions found")
            return
        
        with transaction.atomic():
            for employee in employees_without_position:
                # Assign a default position based on department
                if employee.department:
                    default_position = f"{employee.department.name} Staff"
                else:
                    default_position = "General Staff"
                
                employee.position = default_position
                employee.save()
                print(f"   ✅ Assigned position '{default_position}' to {employee.user.get_full_name()}")
                self.fixes_applied.append(f"Assigned position to {employee.user.get_full_name()}")
    
    def fix_employment_status(self):
        """Fix invalid employment status values"""
        print("\n🔧 FIXING EMPLOYMENT STATUS")
        print("-" * 30)
        
        # Get employees with invalid status
        invalid_status_employees = Employee.objects.exclude(
            employment_status__in=['active', 'inactive', 'terminated', 'on_leave']
        )
        
        if not invalid_status_employees.exists():
            print("✅ No invalid employment status found")
            return
        
        with transaction.atomic():
            for employee in invalid_status_employees:
                old_status = employee.employment_status
                # Default to 'active' for most cases
                employee.employment_status = 'active'
                employee.save()
                print(f"   ✅ Fixed status for {employee.user.get_full_name()}: '{old_status}' → 'active'")
                self.fixes_applied.append(f"Fixed employment status for {employee.user.get_full_name()}")
    
    def fix_duplicate_employee_ids(self):
        """Fix duplicate employee IDs"""
        print("\n🔧 FIXING DUPLICATE EMPLOYEE IDs")
        print("-" * 30)
        
        from django.db.models import Count
        
        # Find duplicate employee IDs
        duplicates = Employee.objects.values('employee_id').annotate(
            count=Count('employee_id')
        ).filter(count__gt=1, employee_id__isnull=False)
        
        if not duplicates.exists():
            print("✅ No duplicate employee IDs found")
            return
        
        # Get the highest existing employee ID number for generating new ones
        existing_ids = Employee.objects.filter(
            employee_id__isnull=False
        ).values_list('employee_id', flat=True)
        
        max_num = 0
        for emp_id in existing_ids:
            if emp_id and emp_id.startswith('EMP'):
                try:
                    num = int(emp_id[3:])
                    max_num = max(max_num, num)
                except ValueError:
                    continue
        
        with transaction.atomic():
            for duplicate in duplicates:
                emp_id = duplicate['employee_id']
                employees_with_id = Employee.objects.filter(employee_id=emp_id)
                
                # Keep the first one, change the others
                for i, employee in enumerate(employees_with_id[1:], 1):
                    max_num += 1
                    new_id = f"EMP{max_num:03d}"
                    old_id = employee.employee_id
                    employee.employee_id = new_id
                    employee.save()
                    print(f"   ✅ Changed duplicate ID for {employee.user.get_full_name()}: {old_id} → {new_id}")
                    self.fixes_applied.append(f"Fixed duplicate ID for {employee.user.get_full_name()}")
    
    def standardize_names(self):
        """Standardize name formatting"""
        print("\n🔧 STANDARDIZING NAME FORMATTING")
        print("-" * 30)
        
        users = User.objects.all()
        fixes_count = 0
        
        with transaction.atomic():
            for user in users:
                changed = False
                
                # Standardize first name
                if user.first_name and user.first_name != user.first_name.title():
                    old_name = user.first_name
                    user.first_name = user.first_name.title()
                    changed = True
                
                # Standardize last name
                if user.last_name and user.last_name != user.last_name.title():
                    old_name = user.last_name
                    user.last_name = user.last_name.title()
                    changed = True
                
                if changed:
                    user.save()
                    fixes_count += 1
                    print(f"   ✅ Standardized name for {user.get_full_name()}")
        
        if fixes_count == 0:
            print("✅ All names are already properly formatted")
        else:
            self.fixes_applied.append(f"Standardized {fixes_count} user names")
    
    def run_full_data_quality_fix(self):
        """Run complete data quality analysis and fixes"""
        print("📊 NUMU EMS DATA QUALITY ANALYSIS & FIXES")
        print("=" * 60)
        
        # Analyze current state
        analysis = self.analyze_employee_data()
        
        # Apply fixes
        print("\n🔧 APPLYING DATA QUALITY FIXES")
        print("=" * 60)
        
        self.fix_missing_employee_ids()
        self.fix_future_hire_dates()
        self.fix_missing_positions()
        self.fix_employment_status()
        self.fix_duplicate_employee_ids()
        self.standardize_names()
        
        # Re-analyze to show improvements
        print("\n📊 POST-FIX DATA QUALITY ANALYSIS")
        print("=" * 50)
        
        final_analysis = self.analyze_employee_data()
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 DATA QUALITY FIXES SUMMARY")
        print("=" * 60)
        
        print(f"✅ Total fixes applied: {len(self.fixes_applied)}")
        
        if self.fixes_applied:
            print("\n🔧 Fixes Applied:")
            for fix in self.fixes_applied:
                print(f"   • {fix}")
        
        print(f"\n📊 Data Quality Improvements:")
        print(f"   • Missing Employee IDs: {analysis['missing_employee_id']} → {final_analysis['missing_employee_id']}")
        print(f"   • Future Hire Dates: {analysis['future_hires']} → {final_analysis['future_hires']}")
        print(f"   • Missing Positions: {analysis['missing_position']} → {final_analysis['missing_position']}")
        print(f"   • Invalid Status: {analysis['invalid_status']} → {final_analysis['invalid_status']}")
        print(f"   • Duplicate IDs: {analysis['duplicate_ids']} → {final_analysis['duplicate_ids']}")
        
        return len(self.fixes_applied) > 0

if __name__ == '__main__':
    fixer = DataQualityFixer()
    success = fixer.run_full_data_quality_fix()
    
    if success:
        print(f"\n🎉 Data quality fixes completed successfully!")
    else:
        print(f"\n✅ No data quality issues found - database is clean!")
