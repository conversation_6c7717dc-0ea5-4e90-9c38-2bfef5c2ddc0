#!/usr/bin/env python3
"""
API Endpoint Testing Script for Numu EMS
Tests all API endpoints to identify issues and missing ViewSets
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.test import APIClient

def get_auth_token():
    """Get authentication token for testing"""
    try:
        user = User.objects.first()
        if not user:
            print("❌ No users found in database")
            return None
        
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        print(f"✅ Got auth token for user: {user.username}")
        return access_token
    except Exception as e:
        print(f"❌ Error getting auth token: {e}")
        return None

def test_api_endpoints():
    """Test all API endpoints"""
    print("🔧 BACKEND API ENDPOINT TESTING")
    print("=" * 60)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        return False
    
    # Setup API client
    client = APIClient()
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    # Core endpoints that should work
    core_endpoints = [
        '/api/employees/',
        '/api/departments/',
        '/api/dashboard-stats/',
        '/api/user-profile/',
        '/api/activities/',
        '/api/roles/',
        '/api/user-profiles/',
    ]
    
    # Endpoints that might have missing ViewSets
    problematic_endpoints = [
        '/api/performance-reviews/',
        '/api/payroll/',
        '/api/recruitment/',
        '/api/training/',
        '/api/invoices/',
        '/api/cost-centers/',
    ]
    
    # Export endpoints
    export_endpoints = [
        '/api/export/employees/',
        '/api/export/departments/',
        '/api/export/attendance/',
    ]
    
    print("\n📊 TESTING CORE ENDPOINTS:")
    working_endpoints = []
    broken_endpoints = []
    
    for endpoint in core_endpoints:
        try:
            response = client.get(endpoint)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {endpoint}: OK ({len(response.content)} bytes)")
                working_endpoints.append(endpoint)
            elif status == 401:
                print(f"🔐 {endpoint}: Authentication required")
                broken_endpoints.append((endpoint, "Authentication"))
            elif status == 404:
                print(f"❌ {endpoint}: Not found")
                broken_endpoints.append((endpoint, "Not found"))
            elif status == 500:
                print(f"💥 {endpoint}: Server error")
                broken_endpoints.append((endpoint, "Server error"))
            else:
                print(f"⚠️ {endpoint}: Status {status}")
                broken_endpoints.append((endpoint, f"Status {status}"))
                
        except Exception as e:
            print(f"💥 {endpoint}: Exception - {e}")
            broken_endpoints.append((endpoint, f"Exception: {e}"))
    
    print("\n🚨 TESTING PROBLEMATIC ENDPOINTS:")
    missing_viewsets = []
    
    for endpoint in problematic_endpoints:
        try:
            response = client.get(endpoint)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {endpoint}: OK")
            elif status == 404:
                print(f"❌ {endpoint}: Missing ViewSet")
                missing_viewsets.append(endpoint)
            else:
                print(f"⚠️ {endpoint}: Status {status}")
                
        except Exception as e:
            print(f"💥 {endpoint}: Exception - {e}")
            missing_viewsets.append(endpoint)
    
    print("\n📤 TESTING EXPORT ENDPOINTS:")
    export_issues = []
    
    for endpoint in export_endpoints:
        try:
            response = client.get(endpoint)
            status = response.status_code
            
            if status == 200:
                content_type = response.get('Content-Type', '')
                if 'csv' in content_type or 'excel' in content_type:
                    print(f"✅ {endpoint}: OK ({content_type})")
                else:
                    print(f"⚠️ {endpoint}: Unexpected content type: {content_type}")
            else:
                print(f"❌ {endpoint}: Status {status}")
                export_issues.append(endpoint)
                
        except Exception as e:
            print(f"💥 {endpoint}: Exception - {e}")
            export_issues.append(endpoint)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 SUMMARY:")
    print(f"✅ Working endpoints: {len(working_endpoints)}")
    print(f"❌ Broken endpoints: {len(broken_endpoints)}")
    print(f"🚨 Missing ViewSets: {len(missing_viewsets)}")
    print(f"📤 Export issues: {len(export_issues)}")
    
    if broken_endpoints:
        print("\n🔧 BROKEN ENDPOINTS TO FIX:")
        for endpoint, issue in broken_endpoints:
            print(f"   • {endpoint}: {issue}")
    
    if missing_viewsets:
        print("\n🏗️ MISSING VIEWSETS TO CREATE:")
        for endpoint in missing_viewsets:
            print(f"   • {endpoint}")
    
    if export_issues:
        print("\n📤 EXPORT ISSUES TO FIX:")
        for endpoint in export_issues:
            print(f"   • {endpoint}")
    
    return len(broken_endpoints) == 0 and len(missing_viewsets) == 0

if __name__ == '__main__':
    success = test_api_endpoints()
    
    if success:
        print("\n🎉 All API endpoints are working correctly!")
    else:
        print("\n⚠️ Some API endpoints need attention.")
        sys.exit(1)
