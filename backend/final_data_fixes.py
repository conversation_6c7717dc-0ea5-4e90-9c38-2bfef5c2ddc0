#!/usr/bin/env python3
"""
Final targeted fixes for remaining data quality issues
"""

import os
import sys
import django
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Employee, Department
from django.contrib.auth.models import User
from django.db import transaction

def fix_remaining_issues():
    """Fix the remaining data quality issues"""
    print("🔧 FINAL DATA QUALITY FIXES")
    print("=" * 50)
    
    with transaction.atomic():
        # Fix the specific employee with "gngn edcx" name
        try:
            problem_employee = Employee.objects.get(employee_id='EMP031')
            print(f"Found problem employee: {problem_employee.user.get_full_name()}")
            
            # Fix the user's name completely
            problem_employee.user.first_name = "Employee31"
            problem_employee.user.last_name = "User31"
            problem_employee.user.save()
            
            # Fix Arabic names
            problem_employee.first_name_ar = "موظف31"
            problem_employee.last_name_ar = "مستخدم31"
            
            # Fix position
            problem_employee.position = "موظف"
            problem_employee.position_ar = "موظف"
            
            # Fix email
            problem_employee.user.email = "<EMAIL>"
            problem_employee.user.save()
            
            problem_employee.save()
            print(f"✅ Fixed employee EMP031: {problem_employee.user.get_full_name()}")
            
        except Employee.DoesNotExist:
            print("⚠️ Employee EMP031 not found")
        
        # Fix future hire dates
        today = date.today()
        future_employees = Employee.objects.filter(hire_date__gt=today)
        
        for emp in future_employees:
            old_date = emp.hire_date
            # Set to 1 year ago
            emp.hire_date = date(today.year - 1, today.month, today.day)
            emp.save()
            print(f"✅ Fixed future date for {emp.employee_id}: {old_date} → {emp.hire_date}")
        
        # Fix any remaining employees with missing English names
        for employee in Employee.objects.all():
            updated = False
            
            # Check if first name is still problematic
            if (not employee.user.first_name or 
                len(employee.user.first_name) < 2 or
                employee.user.first_name.lower() in ['n/a', 'null', 'undefined']):
                
                employee.user.first_name = f"Employee{employee.id}"
                updated = True
            
            # Check if last name is still problematic
            if (not employee.user.last_name or 
                len(employee.user.last_name) < 2 or
                employee.user.last_name.lower() in ['n/a', 'null', 'undefined']):
                
                employee.user.last_name = f"User{employee.id}"
                updated = True
            
            if updated:
                employee.user.save()
                print(f"✅ Fixed name for {employee.employee_id}: {employee.user.get_full_name()}")
        
        # Fix any remaining invalid positions
        for employee in Employee.objects.all():
            if (employee.position and 
                (len(employee.position) < 3 or 
                 employee.position.lower() in ['qwd2wd', 'test', 'dummy', 'null'])):
                
                employee.position = "موظف"
                employee.position_ar = "موظف"
                employee.save()
                print(f"✅ Fixed position for {employee.employee_id}")
        
        print("\n🎉 All remaining data quality issues have been resolved!")

if __name__ == '__main__':
    fix_remaining_issues()
