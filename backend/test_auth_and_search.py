#!/usr/bin/env python3
"""
Test Authentication and Search Functionality
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

def test_auth_and_search():
    """Test authentication and search functionality"""
    print("🧪 TESTING AUTHENTICATION AND SEARCH")
    print("=" * 50)
    
    # Get a user for testing
    user = User.objects.first()
    if not user:
        print("❌ No users found in database")
        return False
    
    # Generate tokens
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    print(f"✅ Generated token for user: {user.username}")
    print(f"📝 Token length: {len(access_token)}")
    
    # Test API endpoints with authentication
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    base_url = 'http://localhost:8000'
    
    # Test 1: Basic employee API
    print("\n🔍 Testing Employee API:")
    response = requests.get(f'{base_url}/api/employees/', headers=headers)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        total_employees = len(data.get('results', []))
        print(f"   ✅ Total employees: {total_employees}")
        
        # Test 2: Search functionality
        print("\n🔍 Testing Search Functionality:")
        search_terms = ['john', 'admin', 'ahmed']
        
        for term in search_terms:
            search_response = requests.get(
                f'{base_url}/api/employees/?search={term}', 
                headers=headers
            )
            
            if search_response.status_code == 200:
                search_data = search_response.json()
                search_results = len(search_data.get('results', []))
                print(f"   ✅ Search '{term}': {search_results} results")
                
                # Show first result for verification
                if search_results > 0:
                    first_result = search_data['results'][0]
                    name = f"{first_result.get('user', {}).get('first_name', '')} {first_result.get('user', {}).get('last_name', '')}"
                    print(f"      First result: {name.strip()}")
            else:
                print(f"   ❌ Search '{term}': Status {search_response.status_code}")
        
        # Test 3: Frontend token format
        print(f"\n🔑 Token for frontend testing:")
        print(f"   Bearer {access_token}")
        
        # Test 4: Create a simple HTML test page
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Search Test</title>
    <script>
        const API_BASE = 'http://localhost:8000';
        const TOKEN = '{access_token}';
        
        async function testSearch() {{
            const searchTerm = document.getElementById('searchInput').value;
            const resultsDiv = document.getElementById('results');
            
            try {{
                const response = await fetch(`${{API_BASE}}/api/employees/?search=${{searchTerm}}`, {{
                    headers: {{
                        'Authorization': `Bearer ${{TOKEN}}`,
                        'Content-Type': 'application/json'
                    }}
                }});
                
                if (response.ok) {{
                    const data = await response.json();
                    const results = data.results || [];
                    
                    resultsDiv.innerHTML = `
                        <h3>Search Results for "${{searchTerm}}" (${{results.length}} found):</h3>
                        <ul>
                            ${{results.map(emp => `
                                <li>${{emp.user?.first_name || ''}} ${{emp.user?.last_name || ''}} - ${{emp.employee_id || 'N/A'}}</li>
                            `).join('')}}
                        </ul>
                    `;
                }} else {{
                    resultsDiv.innerHTML = `<p>Error: ${{response.status}} - ${{response.statusText}}</p>`;
                }}
            }} catch (error) {{
                resultsDiv.innerHTML = `<p>Error: ${{error.message}}</p>`;
            }}
        }}
    </script>
</head>
<body>
    <h1>Employee Search Test</h1>
    <div>
        <input type="text" id="searchInput" placeholder="Search employees..." />
        <button onclick="testSearch()">Search</button>
    </div>
    <div id="results"></div>
</body>
</html>
        """
        
        with open('search_test.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\n📄 Created search_test.html for manual testing")
        print(f"   Open this file in a browser to test search functionality")
        
        return True
    else:
        print(f"   ❌ Employee API failed: {response.status_code}")
        if response.text:
            print(f"   Error: {response.text}")
        return False

if __name__ == '__main__':
    success = test_auth_and_search()
    if success:
        print("\n🎉 Authentication and search testing completed successfully!")
    else:
        print("\n❌ Authentication and search testing failed!")
