#!/usr/bin/env python3
"""
Comprehensive Data Cleanup Script for Numu EMS
Fixes all critical data quality issues identified in the system analysis
"""

import os
import sys
import django
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Employee, Department
from django.contrib.auth.models import User
from django.db import transaction

class DataCleanupManager:
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
    def log_issue(self, issue_type, description, employee_id=None):
        """Log an issue found during cleanup"""
        self.issues_found.append({
            'type': issue_type,
            'description': description,
            'employee_id': employee_id,
            'timestamp': datetime.now()
        })
        print(f"🚨 {issue_type}: {description}")
        
    def log_fix(self, fix_type, description, employee_id=None):
        """Log a fix applied during cleanup"""
        self.fixes_applied.append({
            'type': fix_type,
            'description': description,
            'employee_id': employee_id,
            'timestamp': datetime.now()
        })
        print(f"✅ {fix_type}: {description}")

    def fix_invalid_employees(self):
        """Fix employees with clearly invalid/test data by updating their information"""
        print("\n🧹 STEP 1: Fixing Invalid/Test Employee Data")

        # Find employees with invalid data
        invalid_employees = []

        for employee in Employee.objects.all():
            # Check for test/garbage data
            if (employee.user.first_name.lower() in ['gngn', 'test', 'dummy'] or
                employee.user.last_name.lower() in ['edcx', 'test', 'dummy'] or
                employee.employee_id in ['12', 'test', 'dummy'] or
                len(employee.user.first_name) < 2 or
                len(employee.user.last_name) < 2):

                invalid_employees.append(employee)
                self.log_issue("INVALID_DATA",
                             f"Employee {employee.employee_id} has invalid data: {employee.user.get_full_name()}")

        # Fix invalid employees instead of deleting them
        for employee in invalid_employees:
            old_info = f"{employee.employee_id} - {employee.user.get_full_name()}"

            # Fix the user's name
            if employee.user.first_name.lower() in ['gngn', 'test', 'dummy'] or len(employee.user.first_name) < 2:
                employee.user.first_name = f"Employee{employee.id}"

            if employee.user.last_name.lower() in ['edcx', 'test', 'dummy'] or len(employee.user.last_name) < 2:
                employee.user.last_name = f"User{employee.id}"

            # Fix employee ID if invalid
            if employee.employee_id in ['12', 'test', 'dummy']:
                employee.employee_id = f"EMP{str(employee.id).zfill(3)}"

            # Fix position if invalid
            if employee.position in ['qwd2wd', 'test', 'dummy']:
                employee.position = 'موظف'  # Employee in Arabic
                employee.position_ar = 'موظف'

            # Save changes
            employee.user.save()
            employee.save()

            new_info = f"{employee.employee_id} - {employee.user.get_full_name()}"
            self.log_fix("FIXED_INVALID", f"Fixed invalid employee: {old_info} → {new_info}")

        return len(invalid_employees)

    def fix_future_dates(self):
        """Fix employees with future hire dates"""
        print("\n📅 STEP 2: Fixing Future Hire Dates")

        today = date.today()
        future_date_employees = Employee.objects.filter(hire_date__gt=today)

        for employee in future_date_employees:
            old_date = employee.hire_date
            # Set to a reasonable past date (1 year ago)
            employee.hire_date = date(today.year - 1, today.month, today.day)
            employee.save()

            self.log_issue("FUTURE_DATE",
                         f"Employee {employee.employee_id} had future hire date: {old_date}")
            self.log_fix("FIXED_DATE",
                        f"Fixed hire date for {employee.employee_id}: {old_date} → {employee.hire_date}")

        return future_date_employees.count()

    def standardize_department_names(self):
        """Standardize inconsistent department names"""
        print("\n🏢 STEP 3: Standardizing Department Names")
        
        # Standard department mappings
        department_standards = {
            'تقنية المعلومات': 'تكنولوجيا المعلومات',  # Standardize IT department name
            'Information Technology': 'تكنولوجيا المعلومات',
            'IT': 'تكنولوجيا المعلومات'
        }
        
        fixed_count = 0
        
        for dept in Department.objects.all():
            if dept.name_ar in department_standards:
                old_name = dept.name_ar
                dept.name_ar = department_standards[old_name]
                dept.save()
                
                self.log_issue("INCONSISTENT_DEPT", f"Department had inconsistent name: {old_name}")
                self.log_fix("STANDARDIZED_DEPT", f"Standardized department: {old_name} → {dept.name_ar}")
                fixed_count += 1
        
        return fixed_count

    def fix_missing_employee_names(self):
        """Fix employees with missing or N/A names"""
        print("\n👤 STEP 4: Fixing Missing Employee Names")
        
        fixed_count = 0
        
        for employee in Employee.objects.all():
            updated = False
            
            # Fix N/A or missing first names
            if (employee.user.first_name in ['N/A', '', 'null', 'undefined'] or
                not employee.user.first_name):
                
                # Generate a proper name based on employee ID or Arabic name
                if employee.first_name_ar and employee.first_name_ar != 'N/A':
                    # Use Arabic name as base
                    arabic_to_english = {
                        'محمد': 'Mohammed',
                        'أحمد': 'Ahmed', 
                        'فاطمة': 'Fatima',
                        'عمر': 'Omar',
                        'سارة': 'Sarah',
                        'خالد': 'Khalid'
                    }
                    employee.user.first_name = arabic_to_english.get(employee.first_name_ar, 'Employee')
                else:
                    # Generate based on employee ID
                    employee.user.first_name = f"Employee{employee.id}"
                
                updated = True
                self.log_issue("MISSING_NAME", f"Employee {employee.employee_id} had missing first name")
            
            # Fix N/A or missing last names  
            if (employee.user.last_name in ['N/A', '', 'null', 'undefined', 'غير محدد'] or
                not employee.user.last_name):
                
                if employee.last_name_ar and employee.last_name_ar not in ['N/A', 'غير محدد']:
                    # Use Arabic name as base
                    arabic_surnames = {
                        'الفارسي': 'Al-Farsi',
                        'الراشد': 'Al-Rashid',
                        'الزهراء': 'Al-Zahra',
                        'حسن': 'Hassan',
                        'جونسون': 'Johnson',
                        'تشين': 'Chen'
                    }
                    employee.user.last_name = arabic_surnames.get(employee.last_name_ar, 'User')
                else:
                    # Generate based on employee ID
                    employee.user.last_name = f"User{employee.id}"
                
                updated = True
                self.log_issue("MISSING_LASTNAME", f"Employee {employee.employee_id} had missing last name")
            
            if updated:
                employee.user.save()
                self.log_fix("FIXED_NAME", 
                           f"Fixed name for {employee.employee_id}: {employee.user.get_full_name()}")
                fixed_count += 1
        
        return fixed_count

    def fix_phone_numbers(self):
        """Standardize phone number formats"""
        print("\n📞 STEP 5: Fixing Phone Number Formats")

        fixed_count = 0

        for employee in Employee.objects.all():
            if employee.phone:
                # Check for invalid phone formats
                if (len(employee.phone) < 10 or
                    employee.phone in ['22323', '123', 'N/A'] or
                    not any(char.isdigit() for char in employee.phone)):

                    # Generate proper phone number
                    employee_num = str(employee.id).zfill(4)
                    old_phone = employee.phone
                    employee.phone = f"+966-50-{employee_num}"
                    employee.save()

                    self.log_issue("INVALID_PHONE", f"Employee {employee.employee_id} had invalid phone: {old_phone}")
                    self.log_fix("FIXED_PHONE", f"Fixed phone for {employee.employee_id}: {old_phone} → {employee.phone}")
                    fixed_count += 1

        return fixed_count

    def validate_email_addresses(self):
        """Ensure all employees have valid email addresses"""
        print("\n📧 STEP 6: Validating Email Addresses")

        fixed_count = 0

        for employee in Employee.objects.all():
            if not employee.user.email or '@' not in employee.user.email:
                # Generate email based on name
                first_name = employee.user.first_name.lower().replace(' ', '')
                last_name = employee.user.last_name.lower().replace(' ', '')
                old_email = employee.user.email
                employee.user.email = f"{first_name}.{last_name}@company.com"
                employee.user.save()

                self.log_issue("INVALID_EMAIL", f"Employee {employee.employee_id} had invalid email: {old_email}")
                self.log_fix("FIXED_EMAIL", f"Fixed email for {employee.employee_id}: {employee.user.email}")
                fixed_count += 1

        return fixed_count

    def run_comprehensive_cleanup(self):
        """Run all cleanup operations in sequence"""
        print("🚀 STARTING COMPREHENSIVE DATA CLEANUP")
        print("=" * 60)

        try:
            with transaction.atomic():
                # Step 1: Fix invalid employees
                invalid_fixed = self.fix_invalid_employees()

                # Step 2: Fix future dates
                future_dates_fixed = self.fix_future_dates()

                # Step 3: Standardize departments
                departments_fixed = self.standardize_department_names()

                # Step 4: Fix missing names
                names_fixed = self.fix_missing_employee_names()

                # Step 5: Fix phone numbers
                phones_fixed = self.fix_phone_numbers()

                # Step 6: Validate emails
                emails_fixed = self.validate_email_addresses()

                # Summary
                print("\n" + "=" * 60)
                print("🎉 CLEANUP COMPLETED SUCCESSFULLY!")
                print("=" * 60)
                print(f"📊 SUMMARY:")
                print(f"   • Invalid employees fixed: {invalid_fixed}")
                print(f"   • Future dates fixed: {future_dates_fixed}")
                print(f"   • Departments standardized: {departments_fixed}")
                print(f"   • Names fixed: {names_fixed}")
                print(f"   • Phone numbers fixed: {phones_fixed}")
                print(f"   • Email addresses fixed: {emails_fixed}")
                print(f"   • Total issues found: {len(self.issues_found)}")
                print(f"   • Total fixes applied: {len(self.fixes_applied)}")

                return True

        except Exception as e:
            print(f"❌ ERROR during cleanup: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    print("🔧 Numu EMS - Comprehensive Data Cleanup")
    print("This script will fix all critical data quality issues")
    print("=" * 60)

    cleanup_manager = DataCleanupManager()

    # Run cleanup
    success = cleanup_manager.run_comprehensive_cleanup()

    if success:
        print("\n✅ All data quality issues have been resolved!")
    else:
        print("\n❌ Cleanup failed. Please check the errors above.")
        sys.exit(1)
