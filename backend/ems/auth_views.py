"""
Authentication Views for EMS API
Handles JWT authentication, user registration, password reset, etc.
"""

from rest_framework import status, permissions, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.conf import settings
from django.utils.crypto import get_random_string
from django.utils import timezone
from datetime import timedelta
import logging

from .models import Employee, Role, UserProfile
from .serializers import UserSerializer, EmployeeSerializer, UserProfileSerializer

logger = logging.getLogger(__name__)


class LoginSerializer(serializers.Serializer):
    """Serializer for login credentials"""
    username = serializers.CharField()
    password = serializers.CharField()


class CustomTokenObtainPairView(APIView):
    """
    Custom JWT token obtain view that returns user data along with tokens
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        try:
            # Parse request data manually to handle both DRF and raw requests
            import json

            # Try to get data from different sources
            data = None
            if hasattr(request, 'data') and request.data:
                data = request.data
            elif request.content_type == 'application/json':
                try:
                    data = json.loads(request.body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return Response({
                        'message': 'Invalid JSON data'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = request.POST

            if not data:
                return Response({
                    'message': 'No data provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Use serializer to validate input data
            serializer = LoginSerializer(data=data)
            if not serializer.is_valid():
                return Response({
                    'message': 'Username and password are required',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            username = serializer.validated_data['username']
            password = serializer.validated_data['password']

            # Authenticate user
            user = authenticate(username=username, password=password)

            if not user:
                return Response({
                    'message': 'Invalid credentials'
                }, status=status.HTTP_401_UNAUTHORIZED)

            if not user.is_active:
                return Response({
                    'message': 'User account is disabled'
                }, status=status.HTTP_401_UNAUTHORIZED)

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Get user profile data
            user_data = self._get_user_data(user)

            # Update last login
            user.last_login = timezone.now()
            user.save(update_fields=['last_login'])

            return Response({
                'access': str(access_token),
                'refresh': str(refresh),
                'user': user_data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return Response({
                'message': 'An error occurred during login'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_user_data(self, user):
        """Get comprehensive user data including profile and role information"""
        try:
            # Try to get employee profile
            employee = Employee.objects.select_related(
                'user', 'department', 'manager'
            ).get(user=user)

            # Get user profile for role information
            try:
                user_profile = UserProfile.objects.select_related('role').get(user=user)
                if user_profile.role:
                    # Map role name to frontend role ID
                    role_id_map = {
                        'SUPERADMIN': 'super_admin',
                        'ADMIN': 'admin',
                        'HR_MANAGER': 'hr_manager',
                        'FINANCE_MANAGER': 'finance_manager',
                        'DEPARTMENT_MANAGER': 'department_manager',
                        'PROJECT_MANAGER': 'project_manager',
                        'EMPLOYEE': 'employee'
                    }

                    role_data = {
                        'id': role_id_map.get(user_profile.role.name, 'employee'),
                        'name': user_profile.role.get_name_display(),
                        'nameAr': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions or [],
                        'level': user_profile.role.permissions.get('level', 5) if user_profile.role.permissions else 5,
                        'dashboardConfig': {
                            'allowedRoutes': self._get_allowed_routes(user_profile.role.name),
                            'defaultWidgets': self._get_default_widgets(user_profile.role.name),
                            'customizations': {}
                        }
                    }
                else:
                    # Default role if role is None
                    role_data = {
                        'id': 'employee',
                        'name': 'Employee',
                        'nameAr': 'موظف',
                        'permissions': [],
                        'level': 5,
                        'dashboardConfig': {
                            'allowedRoutes': ['/dashboard', '/profile'],
                            'defaultWidgets': ['tasks', 'schedule'],
                            'customizations': {}
                        }
                    }
            except UserProfile.DoesNotExist:
                # Default role if no profile exists
                role_data = {
                    'id': 'employee',
                    'name': 'Employee',
                    'nameAr': 'موظف',
                    'permissions': [],
                    'dashboardConfig': {
                        'allowedRoutes': ['/dashboard', '/profile'],
                        'defaultWidgets': ['tasks', 'schedule'],
                        'customizations': {}
                    }
                }

            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': role_data,
                'profile': {
                    'avatar': None,  # Add avatar field to Employee model if needed
                    'phone': employee.phone,
                    'department': employee.department.name if employee.department else None,
                    'position': employee.position,
                    'preferred_language': 'ar',  # Default to Arabic
                    'timezone': 'Asia/Riyadh'
                }
            }

        except Employee.DoesNotExist:
            # User exists but no employee profile - check for UserProfile (for ADMIN, SUPERADMIN, etc.)
            try:
                user_profile = UserProfile.objects.select_related('role').get(user=user)
                if user_profile.role:
                    # Map role name to frontend role ID
                    role_id_map = {
                        'SUPERADMIN': 'super_admin',
                        'ADMIN': 'admin',
                        'HR_MANAGER': 'hr_manager',
                        'FINANCE_MANAGER': 'finance_manager',
                        'DEPARTMENT_MANAGER': 'department_manager',
                        'PROJECT_MANAGER': 'project_manager',
                        'EMPLOYEE': 'employee'
                    }

                    role_data = {
                        'id': role_id_map.get(user_profile.role.name, 'employee'),
                        'name': user_profile.role.get_name_display(),
                        'nameAr': user_profile.role.name_ar,
                        'permissions': user_profile.role.permissions or [],
                        'level': user_profile.role.permissions.get('level', 5) if user_profile.role.permissions else 5,
                        'dashboardConfig': {
                            'allowedRoutes': self._get_allowed_routes(user_profile.role.name),
                            'defaultWidgets': self._get_default_widgets(user_profile.role.name),
                            'customizations': {}
                        }
                    }

                    return {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'role': role_data,
                        'profile': {
                            'avatar': None,
                            'phone': user_profile.phone,
                            'department': None,  # UserProfile doesn't have department
                            'position': None,    # UserProfile doesn't have position
                            'preferred_language': user_profile.preferred_language,
                            'timezone': user_profile.timezone
                        }
                    }

            except UserProfile.DoesNotExist:
                pass

            # Fallback for users with no profile at all
            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': {
                    'id': 'user',
                    'name': 'User',
                    'nameAr': 'مستخدم',
                    'permissions': [],
                    'dashboardConfig': {
                        'allowedRoutes': ['/profile'],
                        'defaultWidgets': [],
                        'customizations': {}
                    }
                },
                'profile': {
                    'avatar': None,
                    'phone': None,
                    'department': None,
                    'position': None,
                    'preferred_language': 'ar',
                    'timezone': 'Asia/Riyadh'
                }
            }

    def _get_allowed_routes(self, role_name):
        """Get allowed routes based on role"""
        route_map = {
            'SUPERADMIN': ['/admin/*', '/hr/*', '/finance/*', '/department/*', '/projects/*', '/employee/*'],
            'ADMIN': ['/admin/*'],
            'HR_MANAGER': ['/hr/*'],
            'FINANCE_MANAGER': ['/finance/*'],
            'DEPARTMENT_MANAGER': ['/department/*'],
            'PROJECT_MANAGER': ['/projects/*'],
            'EMPLOYEE': ['/employee/*']
        }
        return route_map.get(role_name, ['/employee/*'])

    def _get_default_widgets(self, role_name):
        """Get default widgets based on role"""
        widget_map = {
            'SUPERADMIN': ['system_overview', 'user_analytics', 'performance_metrics', 'security_dashboard', 'compliance_overview', 'ai_insights'],
            'ADMIN': ['system_overview', 'user_analytics', 'performance_metrics'],
            'HR_MANAGER': ['employee_stats', 'leave_requests', 'attendance_summary'],
            'FINANCE_MANAGER': ['budget_overview', 'expense_tracking', 'financial_reports'],
            'DEPARTMENT_MANAGER': ['team_overview', 'project_progress', 'department_metrics'],
            'PROJECT_MANAGER': ['project_status', 'task_overview', 'team_performance'],
            'EMPLOYEE': ['my_tasks', 'my_schedule', 'my_attendance']
        }
        return widget_map.get(role_name, ['my_tasks', 'my_schedule'])


class CustomTokenRefreshView(TokenRefreshView):
    """
    Custom token refresh view with better error handling
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        try:
            return super().post(request, *args, **kwargs)
        except TokenError as e:
            return Response({
                'message': 'Invalid or expired refresh token'
            }, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            return Response({
                'message': 'An error occurred during token refresh'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogoutView(APIView):
    """
    Logout view that blacklists the refresh token
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({
                'message': 'Successfully logged out'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            return Response({
                'message': 'An error occurred during logout'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile_view(request):
    """
    Get current authenticated user's profile
    """
    try:
        # Use the same method as login to get user data
        token_view = CustomTokenObtainPairView()
        user_data = token_view._get_user_data(request.user)

        return Response(user_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"User profile error: {str(e)}")
        return Response({
            'message': 'An error occurred while fetching user profile'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_profile_view(request):
    """
    Update current user's profile
    """
    try:
        user = request.user
        data = request.data

        # Update User model fields
        if 'first_name' in data:
            user.first_name = data['first_name']
        if 'last_name' in data:
            user.last_name = data['last_name']
        if 'email' in data:
            user.email = data['email']

        user.save()

        # Update Employee model fields if exists
        try:
            employee = Employee.objects.get(user=user)
            if 'phone' in data.get('profile', {}):
                employee.phone = data['profile']['phone']
            employee.save()
        except Employee.DoesNotExist:
            pass

        # Return updated user data
        token_view = CustomTokenObtainPairView()
        user_data = token_view._get_user_data(user)

        return Response(user_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Profile update error: {str(e)}")
        return Response({
            'message': 'An error occurred while updating profile'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password_view(request):
    """
    Change user password
    """
    try:
        user = request.user
        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')

        if not current_password or not new_password:
            return Response({
                'message': 'Current password and new password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Verify current password
        if not user.check_password(current_password):
            return Response({
                'message': 'Current password is incorrect'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Set new password
        user.set_password(new_password)
        user.save()

        return Response({
            'message': 'Password changed successfully'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Password change error: {str(e)}")
        return Response({
            'message': 'An error occurred while changing password'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
