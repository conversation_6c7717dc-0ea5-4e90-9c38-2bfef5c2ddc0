# Generated by Django 4.2.7 on 2025-07-15 03:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0013_add_arabic_name_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='Workflow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('category', models.CharField(choices=[('hr', 'Human Resources'), ('finance', 'Finance'), ('sales', 'Sales'), ('operations', 'Operations'), ('it', 'Information Technology'), ('general', 'General')], default='general', max_length=20)),
                ('category_ar', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('draft', 'Draft'), ('archived', 'Archived')], default='draft', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20)),
                ('trigger_event', models.CharField(blank=True, max_length=100)),
                ('conditions', models.TextField(blank=True)),
                ('conditions_ar', models.TextField(blank=True)),
                ('actions', models.TextField(blank=True)),
                ('actions_ar', models.TextField(blank=True)),
                ('is_automated', models.BooleanField(default=False)),
                ('next_run', models.DateTimeField(blank=True, null=True)),
                ('last_run', models.DateTimeField(blank=True, null=True)),
                ('run_count', models.IntegerField(default=0)),
                ('success_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_workflows', to='ems.employee')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
