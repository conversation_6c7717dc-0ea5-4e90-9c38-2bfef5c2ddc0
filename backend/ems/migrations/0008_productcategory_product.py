# Generated by Django 4.2.7 on 2025-06-05 05:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0007_customer'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(blank=True, max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Product Categories',
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('sku', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('brand', models.CharField(blank=True, max_length=100)),
                ('brand_ar', models.CharField(blank=True, max_length=100)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('cost_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('quantity_in_stock', models.IntegerField(default=0)),
                ('minimum_stock_level', models.IntegerField(default=0)),
                ('maximum_stock_level', models.IntegerField(blank=True, null=True)),
                ('reorder_point', models.IntegerField(default=0)),
                ('unit_of_measure', models.CharField(default='piece', max_length=50)),
                ('unit_of_measure_ar', models.CharField(blank=True, max_length=50)),
                ('barcode', models.CharField(blank=True, max_length=100)),
                ('weight', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True)),
                ('dimensions', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('discontinued', 'Discontinued')], default='active', max_length=20)),
                ('location', models.CharField(blank=True, max_length=100)),
                ('location_ar', models.CharField(blank=True, max_length=100)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='ems.productcategory')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.supplier')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
