# Generated by Django 4.2.7 on 2025-06-05 06:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0008_productcategory_product'),
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('type', models.CharField(choices=[('employee', 'Employee Report'), ('department', 'Department Report'), ('financial', 'Financial Report'), ('performance', 'Performance Report'), ('attendance', 'Attendance Report'), ('payroll', 'Payroll Report')], max_length=20)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('completed_date', models.DateTimeField(blank=True, null=True)),
                ('file_size', models.CharField(blank=True, max_length=50)),
                ('file_url', models.URLField(blank=True)),
                ('parameters', models.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_reports', to='ems.employee')),
            ],
            options={
                'ordering': ['-created_date'],
            },
        ),
    ]
