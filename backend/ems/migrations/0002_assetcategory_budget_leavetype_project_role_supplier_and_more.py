# Generated by Django 4.2.21 on 2025-05-29 18:58

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ems', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Asset Categories',
            },
        ),
        migrations.CreateModel(
            name='Budget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(max_length=200)),
                ('fiscal_year', models.IntegerField()),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('allocated_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('spent_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('remaining_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('name_ar', models.CharField(max_length=50)),
                ('days_allowed', models.IntegerField()),
                ('is_paid', models.BooleanField(default=True)),
                ('requires_approval', models.BooleanField(default=True)),
                ('carry_forward', models.BooleanField(default=False)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('client', models.CharField(blank=True, max_length=200)),
                ('budget_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('actual_start_date', models.DateField(blank=True, null=True)),
                ('actual_end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('PLANNING', 'Planning'), ('IN_PROGRESS', 'In Progress'), ('ON_HOLD', 'On Hold'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='PLANNING', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], default='MEDIUM', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('ADMIN', 'Administrator'), ('HR_MANAGER', 'HR Manager'), ('DEPARTMENT_MANAGER', 'Department Manager'), ('PROJECT_MANAGER', 'Project Manager'), ('FINANCE_MANAGER', 'Finance Manager'), ('EMPLOYEE', 'Employee'), ('INTERN', 'Intern')], max_length=50, unique=True)),
                ('name_ar', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('permissions', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('contact_person', models.CharField(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('website', models.URLField(blank=True)),
                ('tax_number', models.CharField(blank=True, max_length=50)),
                ('payment_terms', models.CharField(blank=True, max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AlterModelOptions(
            name='activity',
            options={'ordering': ['-timestamp'], 'verbose_name_plural': 'Activities'},
        ),
        migrations.AddField(
            model_name='activity',
            name='metadata',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='activity',
            name='related_object_id',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='activity',
            name='related_object_type',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='department',
            name='budget_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='department',
            name='email',
            field=models.EmailField(blank=True, max_length=254),
        ),
        migrations.AddField(
            model_name='department',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='department',
            name='location',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='department',
            name='phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='employee',
            name='bank_account',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='employee',
            name='certifications',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='contract_end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='education',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='emergency_contact',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='employee',
            name='emergency_phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='employee',
            name='employment_status',
            field=models.CharField(choices=[('FULL_TIME', 'Full Time'), ('PART_TIME', 'Part Time'), ('CONTRACT', 'Contract'), ('INTERN', 'Intern'), ('CONSULTANT', 'Consultant')], default='FULL_TIME', max_length=20),
        ),
        migrations.AddField(
            model_name='employee',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subordinates', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='employee',
            name='national_id',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='employee',
            name='passport_number',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='employee',
            name='probation_end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='skills',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='work_location',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AlterField(
            model_name='activity',
            name='activity_type',
            field=models.CharField(choices=[('LOGIN', 'User Login'), ('LOGOUT', 'User Logout'), ('CREATE', 'Record Created'), ('UPDATE', 'Record Updated'), ('DELETE', 'Record Deleted'), ('REPORT', 'Report Generated'), ('APPROVAL', 'Approval Action'), ('MESSAGE', 'Message Sent'), ('MEETING', 'Meeting Action'), ('PROJECT', 'Project Action'), ('TASK', 'Task Action'), ('EXPENSE', 'Expense Action'), ('LEAVE', 'Leave Action'), ('ASSET', 'Asset Action')], max_length=20),
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100)),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=20)),
                ('preferred_language', models.CharField(choices=[('ar', 'Arabic'), ('en', 'English')], default='ar', max_length=2)),
                ('timezone', models.CharField(default='Asia/Riyadh', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('role', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.role')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('due_date', models.DateTimeField()),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('completion_date', models.DateTimeField(blank=True, null=True)),
                ('estimated_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
                ('actual_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
                ('status', models.CharField(choices=[('TODO', 'To Do'), ('IN_PROGRESS', 'In Progress'), ('REVIEW', 'Under Review'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='TODO', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('URGENT', 'Urgent')], default='MEDIUM', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('tags', models.CharField(blank=True, max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_tasks', to='ems.employee')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_tasks', to='ems.employee')),
                ('dependencies', models.ManyToManyField(blank=True, to='ems.task')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='ems.project')),
            ],
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('po_number', models.CharField(max_length=50, unique=True)),
                ('order_date', models.DateField()),
                ('expected_delivery', models.DateField(blank=True, null=True)),
                ('actual_delivery', models.DateField(blank=True, null=True)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('currency', models.CharField(default='SAR', max_length=3)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('ORDERED', 'Ordered'), ('RECEIVED', 'Received'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_pos', to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requested_pos', to='ems.employee')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.supplier')),
            ],
        ),
        migrations.AddField(
            model_name='project',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department'),
        ),
        migrations.AddField(
            model_name='project',
            name='project_manager',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_projects', to='ems.employee'),
        ),
        migrations.AddField(
            model_name='project',
            name='team_members',
            field=models.ManyToManyField(blank=True, related_name='projects', to='ems.employee'),
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('is_important', models.BooleanField(default=False)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='messages/')),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('parent_message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ems.message')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_messages', to='ems.employee')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to='ems.employee')),
            ],
            options={
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('location', models.CharField(blank=True, max_length=200)),
                ('meeting_link', models.URLField(blank=True)),
                ('agenda', models.TextField(blank=True)),
                ('agenda_ar', models.TextField(blank=True)),
                ('minutes', models.TextField(blank=True)),
                ('minutes_ar', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('POSTPONED', 'Postponed')], default='SCHEDULED', max_length=20)),
                ('is_recurring', models.BooleanField(default=False)),
                ('recurrence_pattern', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('attendees', models.ManyToManyField(related_name='meetings', to='ems.employee')),
                ('organizer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organized_meetings', to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('days_requested', models.IntegerField()),
                ('reason', models.TextField()),
                ('reason_ar', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('rejection_reason_ar', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leaves', to='ems.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.leavetype')),
            ],
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True)),
                ('category', models.CharField(choices=[('TRAVEL', 'Travel'), ('OFFICE_SUPPLIES', 'Office Supplies'), ('EQUIPMENT', 'Equipment'), ('SOFTWARE', 'Software'), ('TRAINING', 'Training'), ('MARKETING', 'Marketing'), ('UTILITIES', 'Utilities'), ('RENT', 'Rent'), ('OTHER', 'Other')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='SAR', max_length=3)),
                ('expense_date', models.DateField()),
                ('receipt_file', models.FileField(blank=True, null=True, upload_to='receipts/')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('PAID', 'Paid')], default='PENDING', max_length=20)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_expenses', to='ems.employee')),
                ('budget', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.budget')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.project')),
            ],
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('category', models.CharField(choices=[('POLICY', 'Policy'), ('PROCEDURE', 'Procedure'), ('FORM', 'Form'), ('MANUAL', 'Manual'), ('REPORT', 'Report'), ('CONTRACT', 'Contract'), ('OTHER', 'Other')], max_length=20)),
                ('file', models.FileField(upload_to='documents/')),
                ('version', models.CharField(default='1.0', max_length=20)),
                ('is_public', models.BooleanField(default=False)),
                ('tags', models.CharField(blank=True, max_length=500)),
                ('download_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('access_permissions', models.ManyToManyField(blank=True, related_name='accessible_documents', to='ems.employee')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.department')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
        ),
        migrations.AddField(
            model_name='budget',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee'),
        ),
        migrations.AddField(
            model_name='budget',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='budgets', to='ems.department'),
        ),
        migrations.AddField(
            model_name='budget',
            name='project',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='budgets', to='ems.project'),
        ),
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('asset_id', models.CharField(max_length=50, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('name_ar', models.CharField(blank=True, max_length=200)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True)),
                ('serial_number', models.CharField(blank=True, max_length=100)),
                ('model', models.CharField(blank=True, max_length=100)),
                ('manufacturer', models.CharField(blank=True, max_length=100)),
                ('purchase_date', models.DateField()),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('current_value', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('warranty_expiry', models.DateField(blank=True, null=True)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('status', models.CharField(choices=[('AVAILABLE', 'Available'), ('IN_USE', 'In Use'), ('MAINTENANCE', 'Under Maintenance'), ('RETIRED', 'Retired'), ('LOST', 'Lost'), ('DAMAGED', 'Damaged')], default='AVAILABLE', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.assetcategory')),
            ],
        ),
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200)),
                ('content', models.TextField()),
                ('content_ar', models.TextField(blank=True)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('NORMAL', 'Normal'), ('HIGH', 'High'), ('URGENT', 'Urgent')], default='NORMAL', max_length=10)),
                ('is_published', models.BooleanField(default=False)),
                ('publish_date', models.DateTimeField(blank=True, null=True)),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='announcements/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('target_departments', models.ManyToManyField(blank=True, to='ems.department')),
                ('target_employees', models.ManyToManyField(blank=True, related_name='received_announcements', to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('check_in', models.TimeField(blank=True, null=True)),
                ('check_out', models.TimeField(blank=True, null=True)),
                ('break_start', models.TimeField(blank=True, null=True)),
                ('break_end', models.TimeField(blank=True, null=True)),
                ('total_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=4)),
                ('is_present', models.BooleanField(default=True)),
                ('is_late', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('employee', 'date')},
            },
        ),
    ]
