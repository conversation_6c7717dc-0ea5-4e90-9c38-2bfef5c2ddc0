# Generated by Django 4.2.7 on 2025-05-31 14:27

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ems', '0002_assetcategory_budget_leavetype_project_role_supplier_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplianceFramework',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('region', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('data_protection', 'Data Protection'), ('financial', 'Financial'), ('industry', 'Industry'), ('security', 'Security'), ('labor', 'Labor')], max_length=20)),
                ('version', models.CharField(default='1.0', max_length=20)),
                ('is_active', models.BooleanField(default=False)),
                ('compliance_score', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('last_assessment', models.DateTimeField(blank=True, null=True)),
                ('next_assessment', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='MLModel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('type', models.CharField(choices=[('regression', 'Regression'), ('classification', 'Classification'), ('clustering', 'Clustering'), ('forecasting', 'Forecasting')], max_length=20)),
                ('version', models.CharField(max_length=20)),
                ('accuracy', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('features', models.JSONField(default=list)),
                ('hyperparameters', models.JSONField(default=dict)),
                ('training_data_info', models.JSONField(default=dict)),
                ('status', models.CharField(choices=[('active', 'Active'), ('training', 'Training'), ('deprecated', 'Deprecated')], default='training', max_length=20)),
                ('last_trained', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='Tenant',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('domain', models.CharField(max_length=100, unique=True)),
                ('subdomain', models.CharField(max_length=50, unique=True)),
                ('plan', models.CharField(choices=[('basic', 'Basic'), ('professional', 'Professional'), ('enterprise', 'Enterprise'), ('custom', 'Custom')], default='basic', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('suspended', 'Suspended'), ('trial', 'Trial'), ('expired', 'Expired')], default='trial', max_length=20)),
                ('settings', models.JSONField(default=dict)),
                ('limits', models.JSONField(default=dict)),
                ('features', models.JSONField(default=dict)),
                ('billing_info', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('metadata', models.JSONField(default=dict)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='MLPrediction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('prediction_type', models.CharField(choices=[('employee_performance', 'Employee Performance'), ('turnover_risk', 'Turnover Risk'), ('project_success', 'Project Success'), ('budget_forecast', 'Budget Forecast'), ('resource_demand', 'Resource Demand')], max_length=30)),
                ('input_data', models.JSONField()),
                ('prediction_result', models.JSONField()),
                ('confidence', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('factors', models.JSONField(default=list)),
                ('recommendations', models.JSONField(default=list)),
                ('related_object_type', models.CharField(blank=True, max_length=50)),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('metadata', models.JSONField(default=dict)),
                ('model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.mlmodel')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DataSubjectRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('request_type', models.CharField(choices=[('access', 'Data Access'), ('rectification', 'Data Rectification'), ('erasure', 'Data Erasure'), ('portability', 'Data Portability'), ('restriction', 'Processing Restriction'), ('objection', 'Processing Objection')], max_length=20)),
                ('requester_name', models.CharField(max_length=200)),
                ('requester_email', models.EmailField(max_length=254)),
                ('description', models.TextField()),
                ('status', models.CharField(choices=[('received', 'Received'), ('processing', 'Processing'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='received', max_length=20)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('response', models.TextField(blank=True)),
                ('documents', models.JSONField(default=list)),
                ('metadata', models.JSONField(default=dict)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
            ],
            options={
                'ordering': ['-submitted_at'],
            },
        ),
        migrations.CreateModel(
            name='DataProtectionRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('data_type', models.CharField(max_length=200)),
                ('purpose', models.TextField()),
                ('legal_basis', models.CharField(choices=[('consent', 'Consent'), ('contract', 'Contract'), ('legal_obligation', 'Legal Obligation'), ('vital_interests', 'Vital Interests'), ('public_task', 'Public Task'), ('legitimate_interests', 'Legitimate Interests')], max_length=30)),
                ('data_subjects', models.JSONField(default=list)),
                ('retention_period', models.PositiveIntegerField(help_text='Retention period in years')),
                ('processing_activities', models.JSONField(default=list)),
                ('third_party_sharing', models.BooleanField(default=False)),
                ('cross_border_transfer', models.BooleanField(default=False)),
                ('security_measures', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('data_controller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='controlled_data', to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='ComplianceRequirement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=300)),
                ('description', models.TextField()),
                ('category', models.CharField(max_length=100)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], max_length=20)),
                ('status', models.CharField(choices=[('compliant', 'Compliant'), ('non_compliant', 'Non-Compliant'), ('partial', 'Partial'), ('not_assessed', 'Not Assessed')], default='not_assessed', max_length=20)),
                ('evidence_required', models.JSONField(default=list)),
                ('controls', models.JSONField(default=list)),
                ('last_review', models.DateTimeField(blank=True, null=True)),
                ('next_review', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ems.employee')),
                ('framework', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requirements', to='ems.complianceframework')),
            ],
        ),
        migrations.CreateModel(
            name='ComplianceEvidence',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('document', 'Document'), ('policy', 'Policy'), ('procedure', 'Procedure'), ('training', 'Training'), ('audit', 'Audit'), ('certification', 'Certification')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('file', models.FileField(blank=True, null=True, upload_to='compliance/evidence/')),
                ('url', models.URLField(blank=True)),
                ('status', models.CharField(choices=[('valid', 'Valid'), ('expired', 'Expired'), ('pending_review', 'Pending Review')], default='valid', max_length=20)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('metadata', models.JSONField(default=dict)),
                ('requirement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evidence', to='ems.compliancerequirement')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='ComplianceAssessment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('assessment_type', models.CharField(choices=[('self', 'Self Assessment'), ('internal', 'Internal Audit'), ('external', 'External Audit'), ('regulatory', 'Regulatory Inspection')], max_length=20)),
                ('assessor', models.CharField(max_length=200)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='planned', max_length=20)),
                ('overall_score', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('findings', models.JSONField(default=list)),
                ('recommendations', models.JSONField(default=list)),
                ('next_assessment', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
                ('framework', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='ems.complianceframework')),
            ],
        ),
        migrations.CreateModel(
            name='AutomationRule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('category', models.CharField(choices=[('hr', 'HR'), ('finance', 'Finance'), ('project', 'Project'), ('system', 'System'), ('custom', 'Custom')], max_length=20)),
                ('trigger_config', models.JSONField()),
                ('conditions', models.JSONField(default=list)),
                ('actions', models.JSONField(default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('execution_count', models.PositiveIntegerField(default=0)),
                ('success_count', models.PositiveIntegerField(default=0)),
                ('last_executed', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ems.employee')),
            ],
        ),
        migrations.CreateModel(
            name='AutomationExecution',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('start_time', models.DateTimeField(auto_now_add=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('context_data', models.JSONField(default=dict)),
                ('result_data', models.JSONField(default=dict)),
                ('error_message', models.TextField(blank=True)),
                ('logs', models.JSONField(default=list)),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='ems.automationrule')),
            ],
            options={
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='AuditTrail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action', models.CharField(max_length=100)),
                ('resource', models.CharField(max_length=100)),
                ('resource_id', models.CharField(blank=True, max_length=100)),
                ('old_values', models.JSONField(blank=True, null=True)),
                ('new_values', models.JSONField(blank=True, null=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('compliance_relevant', models.BooleanField(default=False)),
                ('retention_date', models.DateTimeField()),
                ('metadata', models.JSONField(default=dict)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='TenantUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(max_length=50)),
                ('permissions', models.JSONField(default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tenant_users', to='ems.tenant')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('tenant', 'user')},
            },
        ),
    ]
