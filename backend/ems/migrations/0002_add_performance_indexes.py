# Generated migration for performance optimization indexes

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ems', '0001_initial'),
    ]

    operations = [
        # Add indexes for frequently queried fields
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_employee_department ON ems_employee(department_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_department;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_employee_is_active ON ems_employee(is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_is_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_employee_employment_status ON ems_employee(employment_status);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_employment_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_employee_hire_date ON ems_employee(hire_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_hire_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_department_is_active ON ems_department(is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_department_is_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_activity_created_at ON ems_activity(created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_activity_created_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_activity_user ON ems_activity(user_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_activity_user;"
        ),
        
        # Composite indexes for common query patterns
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_employee_dept_active ON ems_employee(department_id, is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_dept_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_employee_status_hire ON ems_employee(employment_status, hire_date);",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_status_hire;"
        ),
        
        # Full-text search indexes for employee search
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_employee_search ON ems_employee USING gin(to_tsvector('english', position || ' ' || COALESCE(position_ar, '')));",
            reverse_sql="DROP INDEX IF EXISTS idx_employee_search;"
        ),
    ]
