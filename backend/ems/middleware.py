"""
Custom middleware for EMS application
"""
from django.http import JsonResponse
from django_ratelimit.exceptions import Ratelimited
import logging

logger = logging.getLogger(__name__)


class RateLimitMiddleware:
    """
    Middleware to handle rate limiting exceptions and return JSON responses
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)

    def process_exception(self, request, exception):
        if isinstance(exception, Ratelimited):
            logger.warning(f"Rate limit exceeded for {request.META.get('REMOTE_ADDR')} on {request.path}")
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': 'Too many requests. Please try again later.',
                'status_code': 429
            }, status=429)
        return None


class SecurityHeadersMiddleware:
    """
    Middleware to add security headers to all responses
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # Add CSP header for API responses
        if request.path.startswith('/api/'):
            response['Content-Security-Policy'] = "default-src 'none'; frame-ancestors 'none';"
        
        return response


class RequestLoggingMiddleware:
    """
    Middleware to log API requests for security monitoring
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Log authentication attempts
        if request.path in ['/api/auth/login/', '/api/auth/refresh/']:
            logger.info(f"Auth attempt from {request.META.get('REMOTE_ADDR')} to {request.path}")
        
        response = self.get_response(request)
        
        # Log failed authentication attempts
        if request.path == '/api/auth/login/' and response.status_code in [400, 401]:
            logger.warning(f"Failed login attempt from {request.META.get('REMOTE_ADDR')}")
        
        return response
