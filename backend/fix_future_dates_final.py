#!/usr/bin/env python3
"""
Final fix for future hire dates
"""

import os
import sys
import django
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Employee
from django.db import transaction

def fix_all_future_dates():
    """Fix all employees with future hire dates"""
    print("📅 FINAL FIX FOR FUTURE HIRE DATES")
    print("=" * 50)
    
    today = date.today()
    print(f"Today's date: {today}")
    
    # Find all employees with future dates
    future_employees = Employee.objects.filter(hire_date__gt=today)
    print(f"Found {future_employees.count()} employees with future dates")
    
    with transaction.atomic():
        for emp in future_employees:
            old_date = emp.hire_date
            
            # Set to a reasonable past date based on employee ID
            if emp.employee_id == 'EMP005':
                emp.hire_date = date(2023, 2, 6)  # 2 years ago
            elif emp.employee_id == 'EMP031':
                emp.hire_date = date(2023, 6, 24)  # 2 years ago
            elif emp.employee_id == 'EMP012':
                emp.hire_date = date(2023, 7, 15)  # 2 years ago
            else:
                # Default: 1 year ago
                emp.hire_date = date(today.year - 1, today.month, today.day)
            
            emp.save()
            print(f"✅ Fixed {emp.employee_id}: {old_date} → {emp.hire_date}")
    
    # Verify no future dates remain
    remaining_future = Employee.objects.filter(hire_date__gt=today)
    if remaining_future.count() == 0:
        print("🎉 All future dates have been fixed!")
    else:
        print(f"⚠️ {remaining_future.count()} future dates still remain")
        for emp in remaining_future:
            print(f"   - {emp.employee_id}: {emp.hire_date}")

if __name__ == '__main__':
    fix_all_future_dates()
