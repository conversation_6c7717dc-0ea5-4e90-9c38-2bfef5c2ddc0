#!/usr/bin/env python3
"""
Comprehensive script to fix Numu EMS data issues
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Department, Employee
from django.contrib.auth.models import User

def fix_employee_data():
    """Fix missing employee data issues"""
    print("🔧 Starting employee data fixes...")

    employees = Employee.objects.all()
    fixed_count = 0

    for employee in employees:
        updated = False

        # Fix missing phone numbers
        if not employee.phone:
            # Generate a phone number based on employee ID
            employee_num = str(employee.id).zfill(4)
            employee.phone = f"******-{employee_num}"
            updated = True
            print(f"📞 Added phone for {employee.user.get_full_name()}: {employee.phone}")

        # Fix missing Arabic names
        if not employee.first_name_ar:
            # Use English name as fallback or create Arabic equivalent
            arabic_names = {
                '<PERSON>': 'أحمد',
                '<PERSON>': 'محمد',
                '<PERSON>': 'محمد',
                'Fatima': 'فاطمة',
                '<PERSON>': 'عمر',
                'Sarah': 'سارة',
                '<PERSON>': 'خالد',
                'Super': 'سوبر',
                'Admin': 'مدير',
                'Employee': 'موظف',
                'User': 'مستخدم'
            }
            employee.first_name_ar = arabic_names.get(employee.user.first_name, employee.user.first_name)
            updated = True
            print(f"🔤 Added Arabic first name for {employee.user.get_full_name()}: {employee.first_name_ar}")

        if not employee.last_name_ar:
            # Use English name as fallback or create Arabic equivalent
            arabic_surnames = {
                'Al-Rashid': 'الراشد',
                'Al-Zahra': 'الزهراء',
                'Hassan': 'حسن',
                'Johnson': 'جونسون',
                'Chen': 'تشين',
                'Rodriguez': 'رودريغيز',
                'Kim': 'كيم',
                'Doe': 'دو',
                'Smith': 'سميث',
                'Mahmoud': 'محمود',
                'Al-Farsi': 'الفارسي'
            }
            employee.last_name_ar = arabic_surnames.get(employee.user.last_name, employee.user.last_name)
            updated = True
            print(f"🔤 Added Arabic last name for {employee.user.get_full_name()}: {employee.last_name_ar}")

        if updated:
            employee.save()
            fixed_count += 1

    print(f"✅ Fixed data for {fixed_count} employees")

def fix_specific_employee_issues():
    """Fix specific employee data issues identified in testing"""
    print("🎯 Fixing specific employee issues...")

    # Fix Employee with ID EMP012 (محمد الفارسي) - missing email
    try:
        emp012 = Employee.objects.get(employee_id='EMP012')
        if not emp012.user.email:
            emp012.user.email = '<EMAIL>'
            emp012.user.save()
            print(f"📧 Fixed email for {emp012.first_name_ar} {emp012.last_name_ar}")
    except Employee.DoesNotExist:
        print("⚠️ Employee EMP012 not found")

    # Fix Employee363 User (AUDIT2363) - missing phone
    try:
        emp363 = Employee.objects.get(employee_id='AUDIT2363')
        if not emp363.phone:
            emp363.phone = '******-0363'
            emp363.save()
            print(f"📞 Fixed phone for {emp363.user.get_full_name()}")
    except Employee.DoesNotExist:
        print("⚠️ Employee AUDIT2363 not found")

    # Fix Employee100 User (TEST3100) - missing phone
    try:
        emp100 = Employee.objects.get(employee_id='TEST3100')
        if not emp100.phone:
            emp100.phone = '******-0100'
            emp100.save()
            print(f"📞 Fixed phone for {emp100.user.get_full_name()}")
    except Employee.DoesNotExist:
        print("⚠️ Employee TEST3100 not found")

def fix_department_data():
    """Fix missing department data"""
    print("🏢 Starting department data fixes...")

    departments = Department.objects.all()
    fixed_count = 0

    for dept in departments:
        updated = False

        # Fix missing Arabic names
        if not dept.name_ar:
            dept_translations = {
                'Human Resources': 'الموارد البشرية',
                'Finance': 'المالية',
                'Information Technology': 'تكنولوجيا المعلومات',
                'IT': 'تقنية المعلومات',
                'Sales': 'المبيعات',
                'Marketing': 'التسويق',
                'Operations': 'العمليات',
                'Customer Service': 'خدمة العملاء',
                'Research & Development': 'البحث والتطوير'
            }
            dept.name_ar = dept_translations.get(dept.name, dept.name)
            updated = True
            print(f"🏢 Added Arabic name for department {dept.name}: {dept.name_ar}")

        if updated:
            dept.save()
            fixed_count += 1

    print(f"✅ Fixed data for {fixed_count} departments")

if __name__ == '__main__':
    print("🚀 Starting Numu EMS Data Fixes...")
    print("=" * 50)

    try:
        fix_employee_data()
        print()
        fix_department_data()
        print()
        fix_specific_employee_issues()
        print()
        print("🎉 All data fixes completed successfully!")

    except Exception as e:
        print(f"❌ Error during data fixes: {e}")
        import traceback
        traceback.print_exc()
