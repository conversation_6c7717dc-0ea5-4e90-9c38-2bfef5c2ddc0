#!/usr/bin/env python3
"""
Performance Analysis Script for Numu EMS
Analyzes database queries, API response times, and identifies optimization opportunities
"""

import os
import sys
import django
import time
from django.db import connection
from django.test.utils import override_settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Employee, Department, Activity
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

class PerformanceAnalyzer:
    def __init__(self):
        self.results = {}
        self.client = APIClient()
        self.setup_auth()
    
    def setup_auth(self):
        """Setup authentication for API testing"""
        user = User.objects.first()
        if user:
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
            print(f"✅ Authentication setup for user: {user.username}")
    
    def analyze_database_queries(self):
        """Analyze database query performance"""
        print("\n🔍 DATABASE QUERY ANALYSIS")
        print("=" * 50)
        
        # Reset query count
        connection.queries_log.clear()
        
        # Test 1: Employee list query
        print("\n📊 Testing Employee List Query:")
        start_time = time.time()
        
        # Current implementation
        employees = list(Employee.objects.select_related('user', 'department').all())
        
        query_time = time.time() - start_time
        query_count = len(connection.queries)
        
        print(f"   • Query count: {query_count}")
        print(f"   • Query time: {query_time:.4f}s")
        print(f"   • Records fetched: {len(employees)}")
        
        # Show actual queries
        if connection.queries:
            print("   • Sample queries:")
            for i, query in enumerate(connection.queries[:3]):
                print(f"     {i+1}. {query['sql'][:100]}...")
        
        self.results['employee_list'] = {
            'query_count': query_count,
            'query_time': query_time,
            'records': len(employees)
        }
        
        # Reset for next test
        connection.queries_log.clear()
        
        # Test 2: Department with employee count
        print("\n🏢 Testing Department Query with Employee Count:")
        start_time = time.time()

        departments = list(Department.objects.prefetch_related('employee_set').all())
        for dept in departments:
            _ = dept.employee_set.count()  # This might cause additional queries
        
        query_time = time.time() - start_time
        query_count = len(connection.queries)
        
        print(f"   • Query count: {query_count}")
        print(f"   • Query time: {query_time:.4f}s")
        print(f"   • Departments: {len(departments)}")
        
        self.results['department_list'] = {
            'query_count': query_count,
            'query_time': query_time,
            'records': len(departments)
        }
        
        return self.results
    
    def analyze_api_performance(self):
        """Analyze API endpoint performance"""
        print("\n🌐 API PERFORMANCE ANALYSIS")
        print("=" * 50)
        
        endpoints_to_test = [
            '/api/employees/',
            '/api/departments/',
            '/api/dashboard-stats/',
            '/api/activities/',
        ]
        
        api_results = {}
        
        for endpoint in endpoints_to_test:
            print(f"\n📡 Testing {endpoint}:")
            
            # Warm up request
            self.client.get(endpoint)
            
            # Measure performance
            start_time = time.time()
            response = self.client.get(endpoint)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                content_size = len(response.content)
                print(f"   • Response time: {response_time:.4f}s")
                print(f"   • Content size: {content_size} bytes")
                print(f"   • Status: ✅ OK")
                
                api_results[endpoint] = {
                    'response_time': response_time,
                    'content_size': content_size,
                    'status': 'OK'
                }
            else:
                print(f"   • Status: ❌ {response.status_code}")
                api_results[endpoint] = {
                    'response_time': response_time,
                    'status': f'Error {response.status_code}'
                }
        
        self.results['api_performance'] = api_results
        return api_results
    
    def analyze_frontend_assets(self):
        """Analyze frontend asset sizes and loading performance"""
        print("\n🎨 FRONTEND ASSET ANALYSIS")
        print("=" * 50)
        
        import os
        import glob
        
        # Check frontend build directory
        frontend_path = "../frontend"
        dist_path = os.path.join(frontend_path, "dist")
        
        if os.path.exists(dist_path):
            print("📦 Analyzing built assets:")
            
            # Find JavaScript files
            js_files = glob.glob(os.path.join(dist_path, "**/*.js"), recursive=True)
            css_files = glob.glob(os.path.join(dist_path, "**/*.css"), recursive=True)
            
            total_js_size = 0
            total_css_size = 0
            
            for js_file in js_files:
                size = os.path.getsize(js_file)
                total_js_size += size
                print(f"   • JS: {os.path.basename(js_file)} - {size/1024:.1f}KB")
            
            for css_file in css_files:
                size = os.path.getsize(css_file)
                total_css_size += size
                print(f"   • CSS: {os.path.basename(css_file)} - {size/1024:.1f}KB")
            
            print(f"\n📊 Asset Summary:")
            print(f"   • Total JS size: {total_js_size/1024:.1f}KB")
            print(f"   • Total CSS size: {total_css_size/1024:.1f}KB")
            print(f"   • Total assets: {(total_js_size + total_css_size)/1024:.1f}KB")
            
            self.results['frontend_assets'] = {
                'js_size': total_js_size,
                'css_size': total_css_size,
                'total_size': total_js_size + total_css_size
            }
        else:
            print("⚠️ Frontend dist directory not found. Run 'npm run build' first.")
    
    def generate_optimization_recommendations(self):
        """Generate performance optimization recommendations"""
        print("\n💡 OPTIMIZATION RECOMMENDATIONS")
        print("=" * 50)
        
        recommendations = []
        
        # Database optimizations
        if 'employee_list' in self.results:
            query_count = self.results['employee_list']['query_count']
            if query_count > 3:
                recommendations.append({
                    'category': 'Database',
                    'priority': 'High',
                    'issue': f'Employee list uses {query_count} queries',
                    'solution': 'Add select_related() and prefetch_related() optimizations'
                })
        
        # API performance optimizations
        if 'api_performance' in self.results:
            for endpoint, data in self.results['api_performance'].items():
                if data.get('response_time', 0) > 1.0:
                    recommendations.append({
                        'category': 'API',
                        'priority': 'Medium',
                        'issue': f'{endpoint} response time: {data["response_time"]:.2f}s',
                        'solution': 'Implement caching and query optimization'
                    })
        
        # Frontend optimizations
        if 'frontend_assets' in self.results:
            total_size = self.results['frontend_assets']['total_size']
            if total_size > 1024 * 1024:  # > 1MB
                recommendations.append({
                    'category': 'Frontend',
                    'priority': 'Medium',
                    'issue': f'Large bundle size: {total_size/1024/1024:.1f}MB',
                    'solution': 'Implement code splitting and lazy loading'
                })
        
        # Display recommendations
        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. [{rec['priority']}] {rec['category']}")
            print(f"   Issue: {rec['issue']}")
            print(f"   Solution: {rec['solution']}")
        
        if not recommendations:
            print("🎉 No major performance issues detected!")
        
        return recommendations
    
    def run_full_analysis(self):
        """Run complete performance analysis"""
        print("🚀 NUMU EMS PERFORMANCE ANALYSIS")
        print("=" * 60)
        
        # Run all analyses
        self.analyze_database_queries()
        self.analyze_api_performance()
        self.analyze_frontend_assets()
        
        # Generate recommendations
        recommendations = self.generate_optimization_recommendations()
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 PERFORMANCE ANALYSIS SUMMARY")
        print("=" * 60)
        
        if 'employee_list' in self.results:
            emp_data = self.results['employee_list']
            print(f"🗃️ Database Performance:")
            print(f"   • Employee queries: {emp_data['query_count']}")
            print(f"   • Query time: {emp_data['query_time']:.4f}s")
        
        if 'api_performance' in self.results:
            api_data = self.results['api_performance']
            avg_response_time = sum(d.get('response_time', 0) for d in api_data.values()) / len(api_data)
            print(f"🌐 API Performance:")
            print(f"   • Average response time: {avg_response_time:.4f}s")
            print(f"   • Endpoints tested: {len(api_data)}")
        
        print(f"💡 Optimization opportunities: {len(recommendations)}")
        
        return self.results, recommendations

if __name__ == '__main__':
    analyzer = PerformanceAnalyzer()
    results, recommendations = analyzer.run_full_analysis()
    
    print(f"\n✅ Performance analysis completed!")
    print(f"📊 Results saved to analyzer.results")
    print(f"💡 {len(recommendations)} optimization recommendations generated")
