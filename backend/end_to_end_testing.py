#!/usr/bin/env python3
"""
End-to-End Testing Script for Numu EMS
Comprehensive testing of user workflows and functionality
"""

import os
import sys
import django
import requests
import time
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.test import APIClient
from ems.models import Employee, Department

class EndToEndTester:
    def __init__(self, frontend_url='http://localhost:5176'):
        self.frontend_url = frontend_url
        self.backend_url = 'http://localhost:8000'
        self.client = APIClient()
        self.issues_found = []
        self.setup_auth()
    
    def setup_auth(self):
        """Setup authentication for testing"""
        try:
            user = User.objects.first()
            if user:
                refresh = RefreshToken.for_user(user)
                access_token = str(refresh.access_token)
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
                print(f"✅ Authentication setup for user: {user.username}")
                return True
        except Exception as e:
            print(f"❌ Could not setup authentication: {e}")
            self.issues_found.append(f"Authentication setup failed: {e}")
            return False
    
    def test_data_consistency(self):
        """Test data consistency between frontend and backend"""
        print("\n📊 TESTING DATA CONSISTENCY")
        print("=" * 50)
        
        # Test 1: Check for future hire dates
        print("\n🔍 Testing Hire Date Consistency:")
        future_hires = Employee.objects.filter(hire_date__gt=date.today())
        
        if future_hires.exists():
            print(f"   ❌ Found {future_hires.count()} employees with future hire dates:")
            for emp in future_hires:
                print(f"      • {emp.user.get_full_name()}: {emp.hire_date}")
                self.issues_found.append(f"Future hire date: {emp.user.get_full_name()} - {emp.hire_date}")
        else:
            print("   ✅ No future hire dates found in database")
        
        # Test 2: Check API response for future dates
        response = self.client.get('/api/employees/')
        if response.status_code == 200:
            data = response.json()
            if 'results' in data:
                future_dates_in_api = []
                for emp in data['results']:
                    hire_date_str = emp.get('hire_date', '')
                    if hire_date_str:
                        try:
                            hire_date = datetime.strptime(hire_date_str, '%Y-%m-%d').date()
                            if hire_date > date.today():
                                future_dates_in_api.append(f"{emp.get('user', {}).get('first_name', '')} {emp.get('user', {}).get('last_name', '')}: {hire_date}")
                        except ValueError:
                            pass
                
                if future_dates_in_api:
                    print(f"   ❌ Found {len(future_dates_in_api)} future dates in API response:")
                    for item in future_dates_in_api:
                        print(f"      • {item}")
                        self.issues_found.append(f"Future date in API: {item}")
                else:
                    print("   ✅ No future dates in API response")
        
        # Test 3: Check for data quality issues
        print("\n🔍 Testing Data Quality:")
        
        # Missing employee IDs
        missing_ids = Employee.objects.filter(employee_id__isnull=True).count()
        if missing_ids > 0:
            print(f"   ❌ {missing_ids} employees missing employee IDs")
            self.issues_found.append(f"{missing_ids} employees missing employee IDs")
        else:
            print("   ✅ All employees have employee IDs")
        
        # Invalid employment status
        invalid_status = Employee.objects.exclude(
            employment_status__in=['active', 'inactive', 'terminated', 'on_leave']
        ).count()
        if invalid_status > 0:
            print(f"   ❌ {invalid_status} employees with invalid employment status")
            self.issues_found.append(f"{invalid_status} employees with invalid employment status")
        else:
            print("   ✅ All employees have valid employment status")
    
    def test_api_functionality(self):
        """Test API functionality and responses"""
        print("\n🔧 TESTING API FUNCTIONALITY")
        print("=" * 50)
        
        # Test core endpoints
        endpoints = [
            ('/api/employees/', 'Employee API'),
            ('/api/departments/', 'Department API'),
            ('/api/dashboard-stats/', 'Dashboard Stats API'),
            ('/api/user-profile/', 'User Profile API'),
        ]
        
        for endpoint, name in endpoints:
            try:
                start_time = time.time()
                response = self.client.get(endpoint)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    print(f"   ✅ {name}: OK ({response_time:.3f}s)")
                    
                    # Check response structure
                    try:
                        data = response.json()
                        if endpoint == '/api/employees/' and 'results' in data:
                            if len(data['results']) == 0:
                                print(f"      ⚠️ No employee data returned")
                                self.issues_found.append(f"{name}: No data returned")
                        elif endpoint == '/api/departments/' and 'results' in data:
                            if len(data['results']) == 0:
                                print(f"      ⚠️ No department data returned")
                                self.issues_found.append(f"{name}: No data returned")
                    except Exception as e:
                        print(f"      ⚠️ Response parsing error: {e}")
                        self.issues_found.append(f"{name}: Response parsing error")
                        
                else:
                    print(f"   ❌ {name}: Status {response.status_code}")
                    self.issues_found.append(f"{name}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"   💥 {name}: Exception - {e}")
                self.issues_found.append(f"{name}: Exception - {e}")
    
    def test_frontend_pages(self):
        """Test frontend page accessibility"""
        print("\n🌐 TESTING FRONTEND PAGES")
        print("=" * 50)
        
        pages = [
            ('/', 'Dashboard'),
            ('/admin/employees', 'Employees Page'),
            ('/admin/departments', 'Departments Page'),
            ('/admin/customers', 'Customers Page'),
            ('/admin/reports', 'Reports Page'),
            ('/admin/settings', 'Settings Page'),
        ]
        
        for path, name in pages:
            try:
                url = f"{self.frontend_url}{path}"
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    content_length = len(response.content)
                    print(f"   ✅ {name}: OK ({content_length} bytes)")
                    
                    # Check for common issues in HTML
                    content = response.text.lower()
                    
                    # Check for error messages
                    if 'error' in content and 'loading' not in content:
                        print(f"      ⚠️ May contain error messages")
                        self.issues_found.append(f"{name}: May contain error messages")
                    
                    # Check for loading states that might be stuck
                    if 'جاري تحميل' in content and content_length < 5000:
                        print(f"      ⚠️ May be stuck in loading state")
                        self.issues_found.append(f"{name}: Stuck in loading state")
                        
                else:
                    print(f"   ❌ {name}: Status {response.status_code}")
                    self.issues_found.append(f"{name}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"   💥 {name}: Exception - {e}")
                self.issues_found.append(f"{name}: Exception - {e}")
    
    def test_user_workflows(self):
        """Test common user workflows"""
        print("\n👤 TESTING USER WORKFLOWS")
        print("=" * 50)
        
        # Test 1: Employee search functionality
        print("\n🔍 Testing Employee Search:")
        search_terms = ['admin', 'john', 'ahmed', 'nonexistent']
        
        for term in search_terms:
            try:
                response = self.client.get(f'/api/employees/?search={term}')
                if response.status_code == 200:
                    data = response.json()
                    results_count = len(data.get('results', []))
                    print(f"   ✅ Search '{term}': {results_count} results")
                    
                    if term == 'nonexistent' and results_count > 0:
                        print(f"      ⚠️ Search for non-existent term returned results")
                        self.issues_found.append(f"Search: Non-existent term '{term}' returned {results_count} results")
                        
                else:
                    print(f"   ❌ Search '{term}': Status {response.status_code}")
                    self.issues_found.append(f"Search '{term}': HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"   💥 Search '{term}': Exception - {e}")
                self.issues_found.append(f"Search '{term}': Exception - {e}")
        
        # Test 2: Department filtering
        print("\n🏢 Testing Department Filtering:")
        try:
            # Get all departments first
            dept_response = self.client.get('/api/departments/')
            if dept_response.status_code == 200:
                departments = dept_response.json().get('results', [])
                
                for dept in departments[:3]:  # Test first 3 departments
                    dept_name = dept.get('name', '')
                    if dept_name:
                        emp_response = self.client.get(f'/api/employees/?department={dept_name}')
                        if emp_response.status_code == 200:
                            emp_data = emp_response.json()
                            emp_count = len(emp_data.get('results', []))
                            print(f"   ✅ Department '{dept_name}': {emp_count} employees")
                        else:
                            print(f"   ❌ Department '{dept_name}': Status {emp_response.status_code}")
                            self.issues_found.append(f"Department filter '{dept_name}': HTTP {emp_response.status_code}")
            else:
                print(f"   ❌ Could not fetch departments: Status {dept_response.status_code}")
                self.issues_found.append(f"Department fetch: HTTP {dept_response.status_code}")
                
        except Exception as e:
            print(f"   💥 Department filtering: Exception - {e}")
            self.issues_found.append(f"Department filtering: Exception - {e}")
    
    def test_ui_responsiveness(self):
        """Test UI responsiveness and mobile compatibility"""
        print("\n📱 TESTING UI RESPONSIVENESS")
        print("=" * 50)
        
        # This would typically require browser automation
        # For now, we'll test the API responses that feed the UI
        
        print("   ℹ️ UI responsiveness testing requires browser automation")
        print("   ℹ️ Testing API responses that feed responsive UI components...")
        
        # Test dashboard stats for mobile display
        try:
            response = self.client.get('/api/dashboard-stats/')
            if response.status_code == 200:
                data = response.json()
                required_stats = ['total_employees', 'total_departments', 'active_employees']
                
                missing_stats = [stat for stat in required_stats if stat not in data]
                if missing_stats:
                    print(f"   ⚠️ Missing dashboard stats: {missing_stats}")
                    self.issues_found.append(f"Dashboard stats missing: {missing_stats}")
                else:
                    print("   ✅ Dashboard stats complete for responsive display")
            else:
                print(f"   ❌ Dashboard stats: Status {response.status_code}")
                self.issues_found.append(f"Dashboard stats: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   💥 Dashboard stats: Exception - {e}")
            self.issues_found.append(f"Dashboard stats: Exception - {e}")
    
    def generate_issue_report(self):
        """Generate comprehensive issue report"""
        print("\n" + "=" * 60)
        print("🐛 END-TO-END TESTING ISSUE REPORT")
        print("=" * 60)
        
        if not self.issues_found:
            print("\n🎉 NO ISSUES FOUND!")
            print("✅ All end-to-end tests passed successfully")
            print("✅ System is functioning correctly")
            return True
        
        print(f"\n❌ FOUND {len(self.issues_found)} ISSUES:")
        print("-" * 40)
        
        # Categorize issues
        critical_issues = []
        warning_issues = []
        minor_issues = []
        
        for issue in self.issues_found:
            if any(keyword in issue.lower() for keyword in ['exception', 'error', 'failed', 'http 5']):
                critical_issues.append(issue)
            elif any(keyword in issue.lower() for keyword in ['future date', 'missing', 'invalid']):
                warning_issues.append(issue)
            else:
                minor_issues.append(issue)
        
        if critical_issues:
            print(f"\n🚨 CRITICAL ISSUES ({len(critical_issues)}):")
            for i, issue in enumerate(critical_issues, 1):
                print(f"   {i}. {issue}")
        
        if warning_issues:
            print(f"\n⚠️ WARNING ISSUES ({len(warning_issues)}):")
            for i, issue in enumerate(warning_issues, 1):
                print(f"   {i}. {issue}")
        
        if minor_issues:
            print(f"\n📝 MINOR ISSUES ({len(minor_issues)}):")
            for i, issue in enumerate(minor_issues, 1):
                print(f"   {i}. {issue}")
        
        # Priority recommendations
        print(f"\n🎯 PRIORITY RECOMMENDATIONS:")
        if critical_issues:
            print("   1. 🚨 Fix critical issues immediately")
        if warning_issues:
            print("   2. ⚠️ Address warning issues for data quality")
        if minor_issues:
            print("   3. 📝 Consider minor improvements for better UX")
        
        return len(critical_issues) == 0
    
    def run_comprehensive_e2e_tests(self):
        """Run all end-to-end tests"""
        print("🧪 NUMU EMS END-TO-END TESTING")
        print("=" * 60)
        print(f"Testing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            self.test_data_consistency()
            self.test_api_functionality()
            self.test_frontend_pages()
            self.test_user_workflows()
            self.test_ui_responsiveness()
            
            success = self.generate_issue_report()
            
            print(f"\n✅ End-to-end testing completed!")
            print(f"📊 Total issues found: {len(self.issues_found)}")
            
            return success
            
        except Exception as e:
            print(f"\n❌ End-to-end testing failed: {e}")
            self.issues_found.append(f"Testing framework error: {e}")
            return False

if __name__ == '__main__':
    tester = EndToEndTester()
    success = tester.run_comprehensive_e2e_tests()
    
    if success:
        print("\n🎉 System passed end-to-end testing!")
        sys.exit(0)
    else:
        print("\n⚠️ System has issues that need attention.")
        sys.exit(1)
