#!/usr/bin/env python3
"""
Comprehensive Testing Script for Numu EMS
Tests all functionality across frontend and backend
"""

import os
import sys
import django
import requests
import time
from urllib.parse import urljoin

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.test import APIClient

class ComprehensiveTester:
    def __init__(self, backend_url='http://localhost:8000', frontend_url='http://localhost:5176'):
        self.backend_url = backend_url
        self.frontend_url = frontend_url
        self.client = APIClient()
        self.setup_auth()
        self.test_results = {
            'backend_api': {},
            'frontend_pages': {},
            'data_quality': {},
            'security': {},
            'performance': {},
            'cross_browser': {}
        }
    
    def setup_auth(self):
        """Setup authentication for testing"""
        try:
            user = User.objects.first()
            if user:
                refresh = RefreshToken.for_user(user)
                access_token = str(refresh.access_token)
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
                print(f"✅ Authentication setup for user: {user.username}")
                return True
        except Exception as e:
            print(f"❌ Could not setup authentication: {e}")
            return False
    
    def test_backend_api_endpoints(self):
        """Test all backend API endpoints"""
        print("\n🔧 TESTING BACKEND API ENDPOINTS")
        print("=" * 50)
        
        endpoints = [
            '/api/employees/',
            '/api/departments/',
            '/api/dashboard-stats/',
            '/api/user-profile/',
            '/api/activities/',
            '/api/roles/',
            '/api/user-profiles/',
        ]
        
        for endpoint in endpoints:
            try:
                response = self.client.get(endpoint)
                status = response.status_code
                
                if status == 200:
                    content_size = len(response.content)
                    print(f"   ✅ {endpoint}: OK ({content_size} bytes)")
                    self.test_results['backend_api'][endpoint] = {
                        'status': 'PASS',
                        'response_code': status,
                        'content_size': content_size
                    }
                else:
                    print(f"   ❌ {endpoint}: Status {status}")
                    self.test_results['backend_api'][endpoint] = {
                        'status': 'FAIL',
                        'response_code': status,
                        'error': f'Unexpected status code: {status}'
                    }
                    
            except Exception as e:
                print(f"   💥 {endpoint}: Exception - {e}")
                self.test_results['backend_api'][endpoint] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
    
    def test_frontend_pages(self):
        """Test frontend page accessibility"""
        print("\n🌐 TESTING FRONTEND PAGES")
        print("=" * 50)
        
        pages = [
            '/',
            '/admin/employees',
            '/admin/departments',
            '/admin/customers',
            '/admin/reports',
            '/admin/settings',
        ]
        
        for page in pages:
            try:
                url = urljoin(self.frontend_url, page)
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    content_size = len(response.content)
                    print(f"   ✅ {page}: OK ({content_size} bytes)")
                    self.test_results['frontend_pages'][page] = {
                        'status': 'PASS',
                        'response_code': response.status_code,
                        'content_size': content_size
                    }
                else:
                    print(f"   ❌ {page}: Status {response.status_code}")
                    self.test_results['frontend_pages'][page] = {
                        'status': 'FAIL',
                        'response_code': response.status_code
                    }
                    
            except Exception as e:
                print(f"   💥 {page}: Exception - {e}")
                self.test_results['frontend_pages'][page] = {
                    'status': 'ERROR',
                    'error': str(e)
                }
    
    def test_data_quality(self):
        """Test data quality and consistency"""
        print("\n📊 TESTING DATA QUALITY")
        print("=" * 50)
        
        from ems.models import Employee, Department
        
        # Test employee data quality
        employees = Employee.objects.all()
        total_employees = employees.count()
        
        # Check for data quality issues
        missing_employee_id = employees.filter(employee_id__isnull=True).count()
        missing_position = employees.filter(position__isnull=True).count()
        missing_department = employees.filter(department__isnull=True).count()
        invalid_status = employees.exclude(
            employment_status__in=['active', 'inactive', 'terminated', 'on_leave']
        ).count()
        
        print(f"   📈 Total employees: {total_employees}")
        print(f"   🔍 Missing employee IDs: {missing_employee_id}")
        print(f"   🔍 Missing positions: {missing_position}")
        print(f"   🔍 Missing departments: {missing_department}")
        print(f"   🔍 Invalid employment status: {invalid_status}")
        
        # Calculate data quality score
        issues = missing_employee_id + missing_position + missing_department + invalid_status
        quality_score = max(0, 100 - (issues * 10))
        
        if quality_score >= 90:
            print(f"   ✅ Data quality score: {quality_score}% (Excellent)")
            status = 'EXCELLENT'
        elif quality_score >= 70:
            print(f"   ⚠️ Data quality score: {quality_score}% (Good)")
            status = 'GOOD'
        else:
            print(f"   ❌ Data quality score: {quality_score}% (Needs improvement)")
            status = 'POOR'
        
        self.test_results['data_quality'] = {
            'status': status,
            'score': quality_score,
            'total_employees': total_employees,
            'issues_found': issues
        }
    
    def test_security_measures(self):
        """Test security implementations"""
        print("\n🔒 TESTING SECURITY MEASURES")
        print("=" * 50)
        
        security_tests = []
        
        # Test unauthenticated access
        unauthenticated_client = APIClient()
        response = unauthenticated_client.get('/api/employees/')
        
        if response.status_code == 401:
            print("   ✅ Authentication protection: Working")
            security_tests.append(True)
        else:
            print("   ❌ Authentication protection: Failed")
            security_tests.append(False)
        
        # Test security headers
        response = self.client.get('/api/employees/')
        security_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
            'Referrer-Policy'
        ]
        
        headers_present = 0
        for header in security_headers:
            if header in response:
                headers_present += 1
        
        if headers_present >= 3:
            print(f"   ✅ Security headers: {headers_present}/{len(security_headers)} present")
            security_tests.append(True)
        else:
            print(f"   ❌ Security headers: {headers_present}/{len(security_headers)} present")
            security_tests.append(False)
        
        # Test input validation
        malicious_input = "'; DROP TABLE employees; --"
        response = self.client.get('/api/employees/', {'search': malicious_input})
        
        if response.status_code == 400:
            print("   ✅ Input validation: Working")
            security_tests.append(True)
        else:
            print("   ⚠️ Input validation: May need attention")
            security_tests.append(False)
        
        # Calculate security score
        security_score = (sum(security_tests) / len(security_tests)) * 100
        
        if security_score >= 80:
            print(f"   ✅ Security score: {security_score:.1f}% (Good)")
            status = 'GOOD'
        else:
            print(f"   ⚠️ Security score: {security_score:.1f}% (Needs improvement)")
            status = 'NEEDS_IMPROVEMENT'
        
        self.test_results['security'] = {
            'status': status,
            'score': security_score,
            'tests_passed': sum(security_tests),
            'total_tests': len(security_tests)
        }
    
    def test_performance(self):
        """Test system performance"""
        print("\n🚀 TESTING PERFORMANCE")
        print("=" * 50)
        
        # Test API response times
        endpoints = ['/api/employees/', '/api/departments/', '/api/dashboard-stats/']
        response_times = []
        
        for endpoint in endpoints:
            start_time = time.time()
            response = self.client.get(endpoint)
            response_time = time.time() - start_time
            response_times.append(response_time)
            
            if response_time < 0.5:
                print(f"   ✅ {endpoint}: {response_time:.3f}s (Excellent)")
            elif response_time < 1.0:
                print(f"   ⚠️ {endpoint}: {response_time:.3f}s (Good)")
            else:
                print(f"   ❌ {endpoint}: {response_time:.3f}s (Slow)")
        
        avg_response_time = sum(response_times) / len(response_times)
        
        if avg_response_time < 0.5:
            status = 'EXCELLENT'
        elif avg_response_time < 1.0:
            status = 'GOOD'
        else:
            status = 'SLOW'
        
        print(f"   📊 Average response time: {avg_response_time:.3f}s")
        
        self.test_results['performance'] = {
            'status': status,
            'avg_response_time': avg_response_time,
            'individual_times': dict(zip(endpoints, response_times))
        }
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📋 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        # Backend API Summary
        backend_passed = sum(1 for result in self.test_results['backend_api'].values() 
                           if result.get('status') == 'PASS')
        backend_total = len(self.test_results['backend_api'])
        
        print(f"\n🔧 Backend API Tests:")
        print(f"   ✅ Passed: {backend_passed}/{backend_total}")
        
        # Frontend Pages Summary
        frontend_passed = sum(1 for result in self.test_results['frontend_pages'].values() 
                            if result.get('status') == 'PASS')
        frontend_total = len(self.test_results['frontend_pages'])
        
        print(f"\n🌐 Frontend Page Tests:")
        print(f"   ✅ Passed: {frontend_passed}/{frontend_total}")
        
        # Data Quality Summary
        data_quality = self.test_results.get('data_quality', {})
        print(f"\n📊 Data Quality:")
        print(f"   📈 Score: {data_quality.get('score', 0)}% ({data_quality.get('status', 'Unknown')})")
        
        # Security Summary
        security = self.test_results.get('security', {})
        print(f"\n🔒 Security:")
        print(f"   🛡️ Score: {security.get('score', 0):.1f}% ({security.get('status', 'Unknown')})")
        
        # Performance Summary
        performance = self.test_results.get('performance', {})
        print(f"\n🚀 Performance:")
        print(f"   ⚡ Average response time: {performance.get('avg_response_time', 0):.3f}s ({performance.get('status', 'Unknown')})")
        
        # Overall Assessment
        total_tests = backend_total + frontend_total
        total_passed = backend_passed + frontend_passed
        overall_score = (total_passed / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n🎯 OVERALL ASSESSMENT:")
        print(f"   📊 Test Success Rate: {overall_score:.1f}% ({total_passed}/{total_tests})")
        
        if overall_score >= 90:
            print("   🎉 System Status: EXCELLENT - Ready for production!")
        elif overall_score >= 80:
            print("   ✅ System Status: GOOD - Minor improvements needed")
        elif overall_score >= 70:
            print("   ⚠️ System Status: FAIR - Some issues need attention")
        else:
            print("   ❌ System Status: POOR - Significant issues need fixing")
        
        return overall_score >= 80
    
    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("🧪 NUMU EMS COMPREHENSIVE TESTING")
        print("=" * 60)
        
        try:
            self.test_backend_api_endpoints()
            self.test_frontend_pages()
            self.test_data_quality()
            self.test_security_measures()
            self.test_performance()
            
            success = self.generate_test_report()
            
            print(f"\n✅ Comprehensive testing completed!")
            return success
            
        except Exception as e:
            print(f"\n❌ Comprehensive testing failed: {e}")
            return False

if __name__ == '__main__':
    tester = ComprehensiveTester()
    success = tester.run_comprehensive_tests()
    
    if success:
        print("\n🎉 All tests passed! System is ready for production.")
        sys.exit(0)
    else:
        print("\n⚠️ Some tests failed. Please review the results above.")
        sys.exit(1)
