#!/usr/bin/env python3
"""
UI/UX Fixes for Numu EMS
Addresses remaining interface and data consistency issues
"""

import os
import sys
import django
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ems.models import Employee, Department
from django.contrib.auth.models import User
from django.db import transaction

def fix_future_hire_dates():
    """Fix employees with future hire dates"""
    print("📅 Fixing Future Hire Dates")
    
    today = date.today()
    future_employees = Employee.objects.filter(hire_date__gt=today)
    
    fixed_count = 0
    for emp in future_employees:
        old_date = emp.hire_date
        # Set to a reasonable past date (1 year ago)
        emp.hire_date = date(today.year - 1, today.month, today.day)
        emp.save()
        print(f"✅ Fixed {emp.employee_id}: {old_date} → {emp.hire_date}")
        fixed_count += 1
    
    print(f"📊 Fixed {fixed_count} future hire dates")
    return fixed_count

def fix_mixed_language_positions():
    """Fix positions that show Arabic text in English interface"""
    print("🌐 Fixing Mixed Language Positions")
    
    # Translation mapping for common Arabic positions
    position_translations = {
        'موظف': 'Employee',
        'مهندس برمجيات': 'Software Engineer',
        'مطور أول': 'Senior Developer',
        'مدير تكنولوجيا المعلومات': 'IT Manager',
        'مدير المشروع': 'Project Manager',
        'مدير المبيعات': 'Sales Manager',
        'أخصائي تسويق': 'Marketing Specialist',
        'مطور برمجيات': 'Software Developer',
        'مدير المالية': 'Finance Manager',
        'مندوب مبيعات': 'Sales Representative',
        'مدير الموارد البشرية': 'HR Manager',
        'الرئيس التنفيذي': 'Chief Executive Officer'
    }
    
    fixed_count = 0
    for employee in Employee.objects.all():
        if employee.position in position_translations:
            old_position = employee.position
            employee.position = position_translations[old_position]
            employee.save()
            print(f"✅ Fixed position for {employee.employee_id}: {old_position} → {employee.position}")
            fixed_count += 1
    
    print(f"📊 Fixed {fixed_count} mixed language positions")
    return fixed_count

def standardize_department_names():
    """Standardize department names for consistency"""
    print("🏢 Standardizing Department Names")
    
    # Department name standardization
    dept_standards = {
        'IT': 'Information Technology',
        'تكنولوجيا المعلومات': 'Information Technology',
        'تقنية المعلومات': 'Information Technology',
        'الموارد البشرية': 'Human Resources',
        'المبيعات': 'Sales',
        'التسويق': 'Marketing',
        'المالية': 'Finance',
        'العمليات': 'Operations'
    }
    
    fixed_count = 0
    
    # Fix department records
    for dept in Department.objects.all():
        if dept.name_ar in dept_standards:
            old_name = dept.name_ar
            dept.name_en = dept_standards[old_name]
            dept.save()
            print(f"✅ Standardized department: {old_name} → {dept.name_en}")
            fixed_count += 1
    
    # Fix employee department references
    for employee in Employee.objects.all():
        if employee.department and employee.department.name_ar in dept_standards:
            print(f"✅ Employee {employee.employee_id} department updated")
    
    print(f"📊 Standardized {fixed_count} department names")
    return fixed_count

def fix_employee_name_consistency():
    """Ensure all employee names are properly formatted"""
    print("👤 Fixing Employee Name Consistency")
    
    fixed_count = 0
    
    for employee in Employee.objects.all():
        updated = False
        
        # Ensure proper English names
        if not employee.user.first_name or len(employee.user.first_name) < 2:
            employee.user.first_name = f"Employee{employee.id}"
            updated = True
        
        if not employee.user.last_name or len(employee.user.last_name) < 2:
            employee.user.last_name = f"User{employee.id}"
            updated = True
        
        # Ensure proper Arabic names
        if not employee.first_name_ar:
            employee.first_name_ar = f"موظف{employee.id}"
            updated = True
            
        if not employee.last_name_ar:
            employee.last_name_ar = f"مستخدم{employee.id}"
            updated = True
        
        if updated:
            employee.user.save()
            employee.save()
            print(f"✅ Fixed names for {employee.employee_id}")
            fixed_count += 1
    
    print(f"📊 Fixed {fixed_count} employee names")
    return fixed_count

def run_ui_ux_fixes():
    """Run all UI/UX fixes"""
    print("🎨 STARTING UI/UX FIXES")
    print("=" * 50)
    
    try:
        with transaction.atomic():
            # Fix 1: Future hire dates
            future_dates_fixed = fix_future_hire_dates()
            
            # Fix 2: Mixed language positions
            positions_fixed = fix_mixed_language_positions()
            
            # Fix 3: Department name standardization
            departments_fixed = standardize_department_names()
            
            # Fix 4: Employee name consistency
            names_fixed = fix_employee_name_consistency()
            
            # Summary
            print("\n" + "=" * 50)
            print("🎉 UI/UX FIXES COMPLETED!")
            print("=" * 50)
            print(f"📊 SUMMARY:")
            print(f"   • Future hire dates fixed: {future_dates_fixed}")
            print(f"   • Mixed language positions fixed: {positions_fixed}")
            print(f"   • Department names standardized: {departments_fixed}")
            print(f"   • Employee names fixed: {names_fixed}")
            print(f"   • Total fixes applied: {future_dates_fixed + positions_fixed + departments_fixed + names_fixed}")
            
            return True
            
    except Exception as e:
        print(f"❌ ERROR during UI/UX fixes: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🎨 Numu EMS - UI/UX Fixes")
    print("This script will fix remaining interface issues")
    print("=" * 50)
    
    success = run_ui_ux_fixes()
    
    if success:
        print("\n✅ All UI/UX issues have been resolved!")
    else:
        print("\n❌ UI/UX fixes failed. Please check the errors above.")
        sys.exit(1)
