
<!DOCTYPE html>
<html>
<head>
    <title>Search Test</title>
    <script>
        const API_BASE = 'http://localhost:8000';
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUyNjU0MDU2LCJpYXQiOjE3NTI2NTA0NTYsImp0aSI6IjJmZGY3ZjczN2UzMTQyY2E5MmRlYjQ0ODBkMmQ0NGI4IiwidXNlcl9pZCI6MX0.SoRMEuM5KqCtNHQg43ph4OCZr_Ozva4-7-7lN0I9BAI';
        
        async function testSearch() {
            const searchTerm = document.getElementById('searchInput').value;
            const resultsDiv = document.getElementById('results');
            
            try {
                const response = await fetch(`${API_BASE}/api/employees/?search=${searchTerm}`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const results = data.results || [];
                    
                    resultsDiv.innerHTML = `
                        <h3>Search Results for "${searchTerm}" (${results.length} found):</h3>
                        <ul>
                            ${results.map(emp => `
                                <li>${emp.user?.first_name || ''} ${emp.user?.last_name || ''} - ${emp.employee_id || 'N/A'}</li>
                            `).join('')}
                        </ul>
                    `;
                } else {
                    resultsDiv.innerHTML = `<p>Error: ${response.status} - ${response.statusText}</p>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
    </script>
</head>
<body>
    <h1>Employee Search Test</h1>
    <div>
        <input type="text" id="searchInput" placeholder="Search employees..." />
        <button onclick="testSearch()">Search</button>
    </div>
    <div id="results"></div>
</body>
</html>
        